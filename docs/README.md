# Code Context Documentation
![](../assets/code_context_logo_dark.png)

Welcome to the Code Context documentation! Code Context is a powerful tool that adds semantic code search capabilities to AI coding assistants through MCP.

## 🚀 Quick Navigation

### Getting Started
- [📋 Project Overview](getting-started/overview.md) - What is Code Context and how it works
- [🛠️ Prerequisites](getting-started/prerequisites.md) - What you need before starting
- [⚡ Quick Start Guide](getting-started/quick-start.md) - Get up and running in 1 minutes


### Components
- [MCP Server](../packages/mcp/README.md) - The MCP server of Code Context
- [VSCode Extension](../packages/vscode-extension/README.md) - The VSCode extension of Code Context
- [Core Package](../packages/core/README.md) - The core package of Code Context

### Troubleshooting
- [❓ FAQ](troubleshooting/faq.md) - Frequently asked questions

## 🔗 External Resources

- [GitHub Repository](https://github.com/zilliztech/code-context)
- [VSCode Marketplace](https://marketplace.visualstudio.com/items?itemName=zilliz.semanticcodesearch)
- [npm - Core Package](https://www.npmjs.com/package/@zilliz/code-context-core)
- [npm - MCP Server](https://www.npmjs.com/package/@zilliz/code-context-mcp)
- [Zilliz Cloud](https://cloud.zilliz.com)

## 💬 Support

- **Issues**: [GitHub Issues](https://github.com/zilliztech/code-context/issues)
- **Discord**: [Join our Discord](https://discord.gg/mKc3R95yE5)
