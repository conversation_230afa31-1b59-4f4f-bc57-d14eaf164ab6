{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "declaration": true, "declarationMap": true, "sourceMap": true, "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "composite": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "baseUrl": ".", "downlevelIteration": true, "paths": {"@zilliz/code-context-core": ["./packages/core/src"], "@zilliz/code-context-core/*": ["./packages/core/src/*"]}}, "references": [{"path": "./packages/core"}, {"path": "./packages/vscode-extension"}, {"path": "./packages/chrome-extension"}, {"path": "./packages/mcp"}], "files": []}