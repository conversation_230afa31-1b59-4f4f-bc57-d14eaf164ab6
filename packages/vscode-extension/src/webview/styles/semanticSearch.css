body {
    font-family: var(--vscode-font-family);
    font-size: var(--vscode-font-size);
    color: var(--vscode-foreground);
    background-color: var(--vscode-editor-background);
    padding: 8px;
    margin: 0;
}

.view {
    width: 100%;
    height: 100%;
}

.search-container {
    margin-bottom: 16px;
}

.header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.title-section {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.header-section h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.header-section .subtitle {
    color: var(--vscode-descriptionForeground);
    font-size: 10px;
    margin: 4px 0 0 0;
    font-weight: 300;
    opacity: 0.7;
    font-style: italic;
    letter-spacing: 0.3px;
}

.settings-button {
    background: none;
    border: none;
    padding: 6px;
    border-radius: 4px;
    cursor: pointer;
    color: var(--vscode-foreground);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.15s ease;
}

.settings-button:hover {
    background-color: var(--vscode-toolbar-hoverBackground);
}

.settings-button:active {
    background-color: var(--vscode-toolbar-activeBackground);
}

.settings-button svg {
    opacity: 0.8;
}

.settings-button:hover svg {
    opacity: 1;
}

.search-input {
    width: 100%;
    padding: 8px;
    border: 1px solid var(--vscode-input-border);
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    border-radius: 4px;
    font-size: 14px;
    box-sizing: border-box;
}

.search-button {
    width: 100%;
    padding: 8px;
    margin-top: 8px;
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.search-button:hover {
    background-color: var(--vscode-button-hoverBackground);
}

.search-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: var(--vscode-input-background) !important;
    color: var(--vscode-descriptionForeground) !important;
    border: 1px solid var(--vscode-input-border);
}

.search-button:disabled:hover {
    background-color: var(--vscode-input-background) !important;
    color: var(--vscode-descriptionForeground) !important;
}

.results-container {
    margin-top: 16px;
}

.results-header {
    color: var(--vscode-descriptionForeground);
    font-size: 12px;
    margin-bottom: 8px;
}

.result-item {
    border: 1px solid var(--vscode-panel-border);
    border-radius: 4px;
    margin-bottom: 8px;
    padding: 12px;
    cursor: pointer;
    background-color: var(--vscode-list-inactiveSelectionBackground);
    transition: background-color 0.1s;
    overflow: hidden;
    position: relative;
}

.result-item:hover {
    background-color: var(--vscode-list-hoverBackground);
}

.result-file {
    font-weight: bold;
    color: var(--vscode-textLink-foreground);
    font-size: 13px;
    margin-bottom: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
}

.result-preview {
    font-family: var(--vscode-editor-font-family);
    font-size: 12px;
    color: var(--vscode-editor-foreground);
    background-color: var(--vscode-textBlockQuote-background);
    padding: 4px 8px;
    border-radius: 3px;
    margin: 4px 0;
    white-space: pre-wrap;
    word-break: break-word;
}

.result-context {
    font-size: 11px;
    color: var(--vscode-descriptionForeground);
    font-style: italic;
}

.result-line {
    font-size: 11px;
    color: var(--vscode-descriptionForeground);
    flex-shrink: 0;
    white-space: nowrap;
}

.result-filename {
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.result-score {
    font-size: 10px;
    color: var(--vscode-descriptionForeground);
    background-color: var(--vscode-badge-background);
    color: var(--vscode-badge-foreground);
    padding: 2px 6px;
    border-radius: 8px;
    font-weight: bold;
    white-space: nowrap;
    display: inline-block;
}

.no-results {
    text-align: center;
    color: var(--vscode-descriptionForeground);
    font-style: italic;
    padding: 20px;
}

/* Settings View Styles */
.container {
    max-width: 600px;
    margin: 0 auto;
    padding: 12px;
}

.header {
    margin-bottom: 16px;
    text-align: center;
}

.header h2 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--vscode-foreground);
    margin-bottom: 4px;
}

.subtitle {
    color: var(--vscode-descriptionForeground);
    font-size: 0.85rem;
    margin: 0;
}

/* Form styles */
form {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.form-group label {
    font-weight: 500;
    color: var(--vscode-foreground);
    font-size: 0.85rem;
}

.form-group select,
.form-group input {
    padding: 6px 10px;
    border: 1px solid var(--vscode-input-border);
    border-radius: 3px;
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    font-size: 0.85rem;
    transition: border-color 0.2s ease;
}

.form-group select:focus,
.form-group input:focus {
    outline: none;
    border-color: var(--vscode-focusBorder);
    box-shadow: 0 0 0 1px var(--vscode-focusBorder);
}

.form-group select:hover,
.form-group input:hover {
    border-color: var(--vscode-input-border);
}

/* Help text */
.help-text {
    font-size: 0.8rem;
    color: var(--vscode-descriptionForeground);
    line-height: 1.4;
}

/* Button group */
.button-group {
    display: flex;
    gap: 8px;
    margin-top: 6px;
    justify-content: flex-end;
}

/* Button styles */
.primary-btn,
.secondary-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 3px;
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 80px;
}

.primary-btn {
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
}

.primary-btn:hover:not(:disabled) {
    background-color: var(--vscode-button-hoverBackground);
}

.secondary-btn {
    background-color: var(--vscode-button-secondaryBackground);
    color: var(--vscode-button-secondaryForeground);
    border: 1px solid var(--vscode-button-border, transparent);
}

.secondary-btn:hover:not(:disabled) {
    background-color: var(--vscode-button-secondaryHoverBackground);
}

.primary-btn:disabled,
.secondary-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Status messages */
.status-message {
    padding: 8px;
    border-radius: 3px;
    font-size: 0.85rem;
    margin-top: 12px;
    border-left: 3px solid;
}

.status-message.success {
    background-color: rgba(22, 163, 74, 0.1);
    color: var(--vscode-foreground);
    border-left-color: #16a34a;
}

.status-message.error {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--vscode-foreground);
    border-left-color: #ef4444;
}

.status-message.info {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--vscode-foreground);
    border-left-color: #3b82f6;
}

/* Select special styles */
select {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
    padding-right: 40px;
}

/* Input focus style optimization */
input[type="password"],
input[type="url"] {
    font-family: monospace;
}

input[type="password"]:focus,
input[type="url"]:focus {
    background-color: var(--vscode-input-background);
}

/* Form separator */
.form-separator {
    height: 1px;
    background-color: var(--vscode-widget-border);
    margin: 16px 0 12px 0;
    opacity: 0.3;
}

/* Additional compact settings for h3 */
.container h3 {
    font-size: 1.1rem;
    margin: 8px 0 4px 0;
    font-weight: 600;
}

/* Model input container styles */
.model-input-container {
    display: flex;
    flex-direction: column;
    gap: 0;
}

.model-select {
    padding: 6px 10px;
    border: 1px solid var(--vscode-input-border);
    border-radius: 3px;
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    font-size: 0.85rem;
    transition: border-color 0.2s ease;
}

.model-select:focus {
    outline: none;
    border-color: var(--vscode-focusBorder);
    box-shadow: 0 0 0 1px var(--vscode-focusBorder);
}

.model-select:hover {
    border-color: var(--vscode-input-border);
}

.model-custom-input {
    padding: 6px 10px;
    border: 1px solid var(--vscode-input-border);
    border-radius: 3px;
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    font-size: 0.85rem;
    transition: border-color 0.2s ease;
}

.model-custom-input:focus {
    outline: none;
    border-color: var(--vscode-focusBorder);
    box-shadow: 0 0 0 1px var(--vscode-focusBorder);
}

.model-custom-input:hover {
    border-color: var(--vscode-input-border);
}