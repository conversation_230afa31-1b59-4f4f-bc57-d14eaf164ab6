// 全局变量
let updateInterval = null;
let currentTasks = [];

// API基础路径
const API_BASE = '/api';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Code Context 索引管理器已启动');
    initializePage();
    startAutoRefresh();
});

// 初始化页面
function initializePage() {
    loadTasks();
    loadProjects();
    updateLastUpdateTime();
}

// 开始自动刷新
function startAutoRefresh() {
    // 每5秒刷新一次任务状态和项目列表
    updateInterval = setInterval(() => {
        loadTasks();
        loadProjects();
        updateLastUpdateTime();
    }, 5000);
}

// 停止自动刷新
function stopAutoRefresh() {
    if (updateInterval) {
        clearInterval(updateInterval);
        updateInterval = null;
    }
}

// 更新最后更新时间
function updateLastUpdateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('zh-CN');
    const lastUpdateElement = document.getElementById('last-update');
    
    if (lastUpdateElement) {
        lastUpdateElement.textContent = timeString;
    }
}

// 加载任务列表
async function loadTasks() {
    const container = document.getElementById('tasks-container');
    const loading = document.getElementById('loading');
    const statusBadge = document.getElementById('status-badge');
    const serverStatus = document.getElementById('server-status');
    
    // 检查必要的DOM元素是否存在
    if (!container || !loading || !statusBadge || !serverStatus) {
        console.error('关键DOM元素未找到:', {
            container: !!container,
            loading: !!loading,
            statusBadge: !!statusBadge,
            serverStatus: !!serverStatus
        });
        return;
    }
    
    try {
        // 显示加载状态
        if (currentTasks.length === 0) {
            loading.style.display = 'flex';
        }
        
        const response = await fetch(`${API_BASE}/tasks`);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.error || '获取任务列表失败');
        }
        
        // 更新服务器状态
        statusBadge.className = 'status-badge online';
        serverStatus.textContent = '服务器在线';
        
        // 更新任务数据
        currentTasks = data.data.tasks;
        
        // 更新概览统计
        updateOverviewStats(data.data);
        
        // 渲染任务列表
        renderTasks(currentTasks);
        
        // 更新页签徽章 - 只更新任务数量，项目数量保持原值
        const currentProjectsCount = parseInt(document.getElementById('projects-badge')?.textContent || '0');
        updateTabBadges(currentTasks.length, currentProjectsCount);
        
    } catch (error) {
        console.error('加载任务失败:', error);
        
        // 更新服务器状态 - 添加null检查
        if (statusBadge) {
            statusBadge.className = 'status-badge offline';
        }
        if (serverStatus) {
            serverStatus.textContent = '服务器连接失败';
        }
        
        // 显示错误信息
        showError('加载任务失败: ' + error.message);
    } finally {
        // 隐藏加载状态 - 添加null检查
        if (loading) {
            loading.style.display = 'none';
        }
    }
}

// 更新概览统计
function updateOverviewStats(data) {
    const activeTasksElement = document.getElementById('active-tasks');
    const indexedCodebasesElement = document.getElementById('indexed-codebases');
    
    if (activeTasksElement) {
        activeTasksElement.textContent = data.totalTasks;
    }
    if (indexedCodebasesElement) {
        indexedCodebasesElement.textContent = data.indexedCodebases;
    }
}

// 渲染任务列表
function renderTasks(tasks) {
    const container = document.getElementById('tasks-container');
    const loading = document.getElementById('loading');
    
    if (!container) {
        console.error('tasks-container 元素未找到');
        return;
    }
    
    // 隐藏loading元素
    if (loading) {
        loading.style.display = 'none';
    }
    
    // 创建或获取任务列表容器
    let tasksList = document.getElementById('tasks-list');
    if (!tasksList) {
        tasksList = document.createElement('div');
        tasksList.id = 'tasks-list';
        container.appendChild(tasksList);
    }
    
    if (tasks.length === 0) {
        tasksList.innerHTML = `
            <div class="empty-state">
                <div class="empty-state-icon">📋</div>
                <h3>暂无索引任务</h3>
                <p>当前没有正在进行或暂停的索引任务。<br>使用 MCP 工具开始新的索引任务。</p>
            </div>
        `;
        return;
    }
    
    const tasksHtml = tasks.map(task => renderTaskCard(task)).join('');
    tasksList.innerHTML = tasksHtml;
}

// 渲染单个任务卡片
function renderTaskCard(task) {
    const statusClass = `status-${task.status}`;
    const statusText = getStatusText(task.status);
    const progressPercentage = parseFloat(task.progress.percentage) || 0;
    
    // 生成操作按钮
    let actionsHtml = '';
    if (task.status === 'indexing') {
        actionsHtml = `
            <button class="btn btn-warning" onclick="pauseTask('${task.taskId}')" title="暂停索引任务">
                <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <rect x="6" y="4" width="4" height="16"/>
                    <rect x="14" y="4" width="4" height="16"/>
                </svg>
                暂停
            </button>
            <button class="btn btn-danger" onclick="clearTask('${task.taskId}')" title="取消并清理此任务">
                <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <circle cx="12" cy="12" r="10"/>
                    <line x1="15" y1="9" x2="9" y2="15"/>
                    <line x1="9" y1="9" x2="15" y2="15"/>
                </svg>
                取消
            </button>
        `;
    } else if (task.status === 'paused') {
        actionsHtml = `
            <button class="btn btn-primary" onclick="resumeTask('${task.taskId}')" title="继续索引任务">
                <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <polygon points="5,3 19,12 5,21"/>
                </svg>
                继续
            </button>
            <button class="btn btn-danger" onclick="clearTask('${task.taskId}')" title="取消并清理此任务">
                <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <circle cx="12" cy="12" r="10"/>
                    <line x1="15" y1="9" x2="9" y2="15"/>
                    <line x1="9" y1="9" x2="15" y2="15"/>
                </svg>
                取消
            </button>
        `;
    } else if (task.status === 'cancelling') {
        // Disable buttons during cancellation
        actionsHtml = `
            <button class="btn btn-warning" disabled title="任务正在取消中">
                <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <rect x="6" y="4" width="4" height="16"/>
                    <rect x="14" y="4" width="4" height="16"/>
                </svg>
                暂停
            </button>
            <button class="btn btn-danger" disabled title="正在取消中，请等待">
                <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <circle cx="12" cy="12" r="3"/>
                    <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
                </svg>
                取消中...
            </button>
        `;
    } else {
        // For completed or error status, only show clear button
        actionsHtml = `
            <button class="btn btn-secondary" onclick="clearTask('${task.taskId}')" title="从列表移除此任务">
                <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <polyline points="3,6 5,6 21,6"/>
                    <path d="M19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/>
                </svg>
                清理
            </button>
        `;
    }
    
    // 错误信息
    const errorHtml = task.lastError ? `
        <div class="error-message">
            <strong>⚠️ 错误:</strong> ${escapeHtml(task.lastError)}
        </div>
    ` : '';
    
    return `
        <div class="task-card">
            <div class="task-header">
                <div class="task-info">
                    <h3>${escapeHtml(task.path)}</h3>
                    <div class="task-id">Task ID: ${task.taskId}</div>
                </div>
                <div class="task-actions">
                    <div class="status-badge ${statusClass}">
                        ${getStatusIcon(task.status)}
                        ${statusText}
                    </div>
                    ${actionsHtml}
                </div>
            </div>
            
            <div class="progress-section">
                <div class="progress-info">
                    <span>进度: ${task.progress.processedFiles} / ${task.progress.totalFiles} 文件</span>
                    <span>${task.progress.percentage}</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${progressPercentage}%"></div>
                </div>
            </div>
            
            <div class="task-details">
                <div class="detail-row">
                    <span class="detail-label">生成代码块:</span>
                    <span class="detail-value">${task.totalChunks.toLocaleString()}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">分割器:</span>
                    <span class="detail-value">${task.splitter.toUpperCase()}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">强制重建:</span>
                    <span class="detail-value">${task.forceReindex ? '是' : '否'}</span>
                </div>
            </div>
            
            ${errorHtml}
        </div>
    `;
}

// 获取状态文本
function getStatusText(status) {
    const statusMap = {
        'indexing': '索引中',
        'paused': '已暂停',
        'completed': '已完成',
        'error': '错误',
        'cancelling': '正在取消'
    };
    return statusMap[status] || status;
}

// 获取状态图标
function getStatusIcon(status) {
    const iconMap = {
        'indexing': `<svg class="status-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="3"/>
            <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
        </svg>`,
        'paused': `<svg class="status-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <rect x="6" y="4" width="4" height="16"/>
            <rect x="14" y="4" width="4" height="16"/>
        </svg>`,
        'completed': `<svg class="status-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
            <polyline points="22,4 12,14.01 9,11.01"/>
        </svg>`,
        'error': `<svg class="status-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="10"/>
            <line x1="15" y1="9" x2="9" y2="15"/>
            <line x1="9" y1="9" x2="15" y2="15"/>
        </svg>`,
        'cancelling': `<svg class="status-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="10"/>
            <path d="M8 12h8"/>
        </svg>`
    };
    return iconMap[status] || '';
}

// 暂停任务
async function pauseTask(taskId) {
    const task = currentTasks.find(t => t.taskId === taskId);
    if (!task) return;
    
    showConfirmationModal(
        '暂停索引任务',
        `确定要暂停任务 "${task.path}" 吗？`,
        () => executeTaskAction('pause', taskId)
    );
}

// 继续任务
async function resumeTask(taskId) {
    const task = currentTasks.find(t => t.taskId === taskId);
    if (!task) return;
    
    showConfirmationModal(
        '继续索引任务',
        `确定要继续任务 "${task.path}" 吗？`,
        () => executeTaskAction('resume', taskId)
    );
}

// 清理任务
async function clearTask(taskId) {
    const task = currentTasks.find(t => t.taskId === taskId);
    if (!task) return;
    
    const statusText = getStatusText(task.status);
    let title = '清理任务';
    let message = `确定要从列表中移除任务 "${task.path}" 吗？<br><br>当前状态: <strong>${statusText}</strong><br><br>此操作无法恢复。`;

    if (task.status === 'indexing' || task.status === 'paused') {
        title = '取消索引任务';
        message = `确定要取消任务 "${task.path}" 吗？<br><br>此操作将停止索引进程并删除所有已处理的进度，操作不可恢复。`;
    }
    
    showConfirmationModal(
        title,
        message,
        () => executeClearTask(taskId)
    );
}

// 执行任务操作
async function executeTaskAction(action, taskId) {
    try {
        const response = await fetch(`${API_BASE}/tasks/${taskId}/${action}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (!response.ok || !data.success) {
            throw new Error(data.error || `${action} 操作失败`);
        }
        
        // 显示成功消息
        showSuccess(data.message);
        
        // 立即刷新任务列表
        loadTasks();
        
    } catch (error) {
        console.error(`${action} 任务失败:`, error);
        showError(`${action === 'pause' ? '暂停' : '继续'}任务失败: ${error.message}`);
    } finally {
        hideModal();
    }
}

// 执行清理任务操作
async function executeClearTask(taskId) {
    try {
        const response = await fetch(`${API_BASE}/tasks/${taskId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (!response.ok || !data.success) {
            throw new Error(data.error || '清理任务失败');
        }
        
        // 显示成功消息
        showSuccess(data.message);
        
        // 立即刷新任务列表和概览统计
        loadTasks();
        
    } catch (error) {
        console.error('清理任务失败:', error);
        showError('清理任务失败: ' + error.message);
    } finally {
        hideModal();
    }
}

// 显示确认对话框
function showConfirmationModal(title, message, onConfirm) {
    const modal = document.getElementById('confirmation-modal');
    const modalTitle = document.getElementById('modal-title');
    const modalMessage = document.getElementById('modal-message');
    const confirmBtn = document.getElementById('confirm-btn');
    
    // 检查必要的DOM元素是否存在
    if (!modal || !modalTitle || !modalMessage || !confirmBtn) {
        console.error('模态对话框元素未找到:', {
            modal: !!modal,
            modalTitle: !!modalTitle,
            modalMessage: !!modalMessage,
            confirmBtn: !!confirmBtn
        });
        return;
    }
    
    modalTitle.textContent = title;
    modalMessage.textContent = message;
    
    // 移除之前的事件监听器
    const newConfirmBtn = confirmBtn.cloneNode(true);
    confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
    
    // 添加新的事件监听器
    newConfirmBtn.addEventListener('click', onConfirm);
    
    // 显示模态对话框
    modal.classList.add('show');
}

// 隐藏模态对话框
function hideModal() {
    const modal = document.getElementById('confirmation-modal');
    
    if (modal) {
        modal.classList.remove('show');
    }
}

// 显示成功消息
function showSuccess(message) {
    console.log('✅ 成功:', message);
    // 可以在这里添加成功提示的UI组件
    showNotification(message, 'success');
}

// 显示错误消息
function showError(message) {
    console.error('❌ 错误:', message);
    // 可以在这里添加错误提示的UI组件
    showNotification(message, 'error');
}

// 显示通知
function showNotification(message, type = 'info') {
    // 先清除可能存在的旧通知，避免堆叠
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(n => n.remove());
    
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-icon">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
            <span class="notification-message">${escapeHtml(message)}</span>
        </div>
        <button class="notification-close" onclick="removeNotification(this.parentElement)">&times;</button>
    `;
    
    // 添加样式
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#d1ecf1'};
        color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460'};
        border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : '#bee5eb'};
        border-radius: 8px;
        padding: 12px 16px;
        max-width: 400px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 1001;
        animation: slideInRight 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 12px;
        opacity: 1;
        transform: translateX(0);
        transition: all 0.3s ease;
    `;
    
    // 添加到页面
    document.body.appendChild(notification);
    
    // 自动移除 - 使用平滑的淡出动画
    setTimeout(() => {
        removeNotification(notification);
    }, 5000);
}

// 安全移除通知的函数
function removeNotification(notification) {
    if (!notification || !notification.parentNode) return;
    
    // 添加淡出动画
    notification.style.opacity = '0';
    notification.style.transform = 'translateX(100%)';
    
    // 动画完成后彻底移除
    setTimeout(() => {
        if (notification && notification.parentNode) {
            notification.remove();
        }
    }, 300);
}

// HTML转义函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 页面可见性变化处理
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        stopAutoRefresh();
    } else {
        startAutoRefresh();
        loadTasks(); // 页面重新可见时立即刷新
        loadProjects(); // 刷新项目列表
    }
});

// 键盘快捷键
document.addEventListener('keydown', function(event) {
    // ESC 键关闭模态对话框
    if (event.key === 'Escape') {
        hideModal();
    }
    
    // F5 或 Ctrl+R 刷新任务
    if (event.key === 'F5' || (event.ctrlKey && event.key === 'r')) {
        event.preventDefault();
        loadTasks();
        loadProjects();
    }
});

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(100%);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    .notification-content {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .notification-close {
        background: none;
        border: none;
        font-size: 1.2rem;
        cursor: pointer;
        opacity: 0.7;
        transition: opacity 0.2s;
    }
    
    .notification-close:hover {
        opacity: 1;
    }
    
    .task-details {
        margin-top: 12px;
        padding-top: 12px;
        border-top: 1px solid #e9ecef;
    }
    
    .detail-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 4px;
        font-size: 0.9rem;
    }
    
    .detail-label {
        color: #6c757d;
        font-weight: 500;
    }
    
    .detail-value {
        color: #495057;
        font-weight: 600;
    }
    
    .error-message {
        margin-top: 12px;
        padding: 8px 12px;
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 6px;
        font-size: 0.85rem;
        color: #856404;
    }
`;
document.head.appendChild(style);

// ========== 项目管理功能 ==========

// 加载已索引项目列表
async function loadProjects() {
    const container = document.getElementById('projects-container');
    const loading = document.getElementById('projects-loading');
    
    if (!container) {
        console.error('projects-container 元素未找到');
        return;
    }
    
    try {
        // 显示加载状态
        if (loading) {
            loading.style.display = 'flex';
        }
        
        const response = await fetch(`${API_BASE}/projects`);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.error || '获取项目列表失败');
        }
        
        // 渲染项目列表
        renderProjects(data.data.projects);
        
        // 更新页签徽章 - 只更新项目数量，任务数量保持原值
        const currentTasksCount = parseInt(document.getElementById('tasks-badge')?.textContent || '0');
        updateTabBadges(currentTasksCount, data.data.projects.length);
        
    } catch (error) {
        console.error('加载项目失败:', error);
        showError('加载项目失败: ' + error.message);
    } finally {
        // 隐藏加载状态
        if (loading) {
            loading.style.display = 'none';
        }
    }
}

// 渲染项目列表
function renderProjects(projects) {
    const container = document.getElementById('projects-container');
    const loading = document.getElementById('projects-loading');
    
    if (!container) {
        console.error('projects-container 元素未找到');
        return;
    }
    
    // 隐藏loading元素
    if (loading) {
        loading.style.display = 'none';
    }
    
    // 创建或获取项目列表容器
    let projectsList = document.getElementById('projects-list');
    if (!projectsList) {
        projectsList = document.createElement('div');
        projectsList.id = 'projects-list';
        container.appendChild(projectsList);
    }
    
    if (projects.length === 0) {
        projectsList.innerHTML = `
            <div class="empty-state">
                <div class="empty-state-icon">📚</div>
                <h3>暂无已索引项目</h3>
                <p>还没有已完成索引的项目。<br>使用上方的"添加新项目"功能开始索引新项目。</p>
            </div>
        `;
        return;
    }
    
    const projectsHtml = projects.map(project => renderProjectCard(project)).join('');
    projectsList.innerHTML = projectsHtml;
}

// 获取项目状态图标
function getProjectStatusIcon(status) {
    const iconMap = {
        'completed': `<svg class="stat-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
            <polyline points="22,4 12,14.01 9,11.01"/>
        </svg>`,
        'indexing': `<svg class="stat-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="3"/>
            <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
        </svg>`,
        'error': `<svg class="stat-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="10"/>
            <line x1="15" y1="9" x2="9" y2="15"/>
            <line x1="9" y1="9" x2="15" y2="15"/>
        </svg>`
    };
    return iconMap[status] || iconMap['error'];
}

// 渲染单个项目卡片
function renderProjectCard(project) {
    const indexedDate = new Date(project.indexedAt).toLocaleString('zh-CN');
    
    let statusClass, statusIcon, statusText;
    switch (project.status) {
        case 'completed':
            statusClass = 'success';
            statusIcon = '✅';
            statusText = '已完成';
            break;
        case 'indexing':
            statusClass = 'indexing';
            statusIcon = '🔄';
            statusText = '索引中...';
            break;
        case 'error':
        default:
            statusClass = 'error';
            statusIcon = '❌';
            statusText = '错误';
            break;
    }
    
    const errorHtml = project.errorMessage ? `
        <div class="error-message">
            <strong>⚠️ 错误:</strong> ${escapeHtml(project.errorMessage)}
        </div>
    ` : '';
    
    // 如果正在索引中，禁用操作按钮
    const actionsDisabled = project.status === 'indexing';
    const reindexBtnClass = actionsDisabled ? 'btn-icon primary disabled' : 'btn-icon primary';
    const clearBtnClass = actionsDisabled ? 'btn-icon danger disabled' : 'btn-icon danger';
    const reindexOnclick = actionsDisabled ? '' : `onclick="reindexProject('${encodeURIComponent(project.path)}')"`;
    const clearOnclick = actionsDisabled ? '' : `onclick="clearProject('${encodeURIComponent(project.path)}')"`;
    
    return `
        <div class="project-item">
            <div class="project-info">
                <div class="project-path">${escapeHtml(project.path)}</div>
                <div class="project-stats">
                    <div class="project-stat">
                        <svg class="stat-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M22 19a2 2 0 01-2 2H4a2 2 0 01-2-2V5a2 2 0 012-2h5l2 3h9a2 2 0 012 2z"/>
                        </svg>
                        <span>${project.totalFiles} 个文件</span>
                    </div>
                    <div class="project-stat">
                        <svg class="stat-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                        <span>${project.totalChunks.toLocaleString()} 个代码块</span>
                    </div>
                    <div class="project-stat">
                        <svg class="stat-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <circle cx="12" cy="12" r="3"/>
                            <path d="M19.4 15a1.65 1.65 0 00.33 1.82l.06.06a2 2 0 010 2.83 2 2 0 01-2.83 0l-.06-.06a1.65 1.65 0 00-1.82-.33 1.65 1.65 0 00-1 1.51V21a2 2 0 01-2 2 2 2 0 01-2-2v-.09A1.65 1.65 0 009 19.4a1.65 1.65 0 00-1.82.33l-.06.06a2 2 0 01-2.83 0 2 2 0 010-2.83l.06-.06a1.65 1.65 0 00.33-1.82 1.65 1.65 0 00-1.51-1H3a2 2 0 01-2-2 2 2 0 012-2h.09A1.65 1.65 0 004.6 9a1.65 1.65 0 00-.33-1.82l-.06-.06a2 2 0 010-2.83 2 2 0 012.83 0l.06.06a1.65 1.65 0 001.82.33H9a1.65 1.65 0 001-1.51V3a2 2 0 012-2 2 2 0 012 2v.09a1.65 1.65 0 001 1.51 1.65 1.65 0 001.82-.33l.06-.06a2 2 0 012.83 0 2 2 0 010 2.83l-.06.06a1.65 1.65 0 00-.33 1.82V9a1.65 1.65 0 001.51 1H21a2 2 0 012 2 2 2 0 01-2 2h-.09a1.65 1.65 0 00-1.51 1z"/>
                        </svg>
                        <span>${project.splitter.toUpperCase()}</span>
                    </div>
                    <div class="project-stat status-${statusClass}">
                        ${getProjectStatusIcon(project.status)}
                        <span>${statusText}</span>
                    </div>
                </div>
                <div class="project-meta">
                    <span>索引时间: ${indexedDate}</span>
                    ${project.lastSearchAt ? `<span>最后搜索: ${new Date(project.lastSearchAt).toLocaleString('zh-CN')}</span>` : ''}
                </div>
                ${errorHtml}
            </div>
            <div class="project-actions">
                <button class="btn btn-secondary ${actionsDisabled ? 'disabled' : ''}" ${reindexOnclick} title="重新索引此项目" ${actionsDisabled ? 'disabled' : ''}>
                    <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M23 4v6h-6M1 20v-6h6"/>
                        <path d="M20.49 9A9 9 0 00 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 01 3.51 15"/>
                    </svg>
                    重新索引
                </button>
                <button class="btn btn-danger ${actionsDisabled ? 'disabled' : ''}" ${clearOnclick} title="清除此项目的索引数据" ${actionsDisabled ? 'disabled' : ''}>
                    <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <polyline points="3,6 5,6 21,6"/>
                        <path d="M19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/>
                        <line x1="10" y1="11" x2="10" y2="17"/>
                        <line x1="14" y1="11" x2="14" y2="17"/>
                    </svg>
                    删除
                </button>
            </div>
        </div>
    `;
}

// 添加新项目
async function addNewProject() {
    const pathInput = document.getElementById('project-path');
    const splitterSelect = document.getElementById('splitter-type');
    
    if (!pathInput || !splitterSelect) {
        showError('表单元素未找到');
        return;
    }
    
    const projectPath = pathInput.value.trim();
    const splitterType = splitterSelect.value;
    
    if (!projectPath) {
        showError('请输入项目目录路径');
        pathInput.focus();
        return;
    }
    
    try {
        const response = await fetch(`${API_BASE}/projects`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                path: projectPath,
                splitter: splitterType
            })
        });
        
        const data = await response.json();
        
        if (!response.ok || !data.success) {
            throw new Error(data.error || '添加项目失败');
        }
        
        // 显示成功消息
        showSuccess(data.message);
        
        // 清空表单
        pathInput.value = '';
        splitterSelect.value = 'ast';
        
        // 刷新任务和项目列表
        loadTasks();
        loadProjects();
        
    } catch (error) {
        console.error('添加项目失败:', error);
        showError('添加项目失败: ' + error.message);
    }
}

// 清除项目索引
async function clearProject(encodedPath) {
    const projectPath = decodeURIComponent(encodedPath);
    
    showConfirmationModal(
        '清除项目索引',
        `确定要清除项目 "${projectPath}" 的索引吗？此操作不可恢复。`,
        async () => {
            try {
                const response = await fetch(`${API_BASE}/projects/${encodedPath}`, {
                    method: 'DELETE'
                });
                
                const data = await response.json();
                
                if (!response.ok || !data.success) {
                    throw new Error(data.error || '清除索引失败');
                }
                
                // 显示成功消息
                showSuccess(data.message);
                
                // 刷新项目列表
                loadProjects();
                
            } catch (error) {
                console.error('清除项目索引失败:', error);
                showError('清除项目索引失败: ' + error.message);
            } finally {
                hideModal();
            }
        }
    );
}

// 重新索引项目
async function reindexProject(encodedPath) {
    const projectPath = decodeURIComponent(encodedPath);
    
    // 创建选择分词器的模态框
    const modal = document.getElementById('confirmation-modal');
    const modalTitle = document.getElementById('modal-title');
    const modalMessage = document.getElementById('modal-message');
    const confirmBtn = document.getElementById('confirm-btn');
    
    if (!modal || !modalTitle || !modalMessage || !confirmBtn) {
        showError('模态对话框元素未找到');
        return;
    }
    
    modalTitle.textContent = '重新索引项目';
    modalMessage.innerHTML = `
        <p>确定要重新索引项目 "${escapeHtml(projectPath)}" 吗？</p>
        <div class="form-group" style="margin-top: 16px;">
            <label for="reindex-splitter-type">选择分词器类型:</label>
            <select id="reindex-splitter-type" class="select-input">
                <option value="ast">AST (推荐)</option>
                <option value="langchain">LangChain</option>
            </select>
        </div>
    `;
    
    // 移除之前的事件监听器
    const newConfirmBtn = confirmBtn.cloneNode(true);
    confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
    
    // 添加新的事件监听器
    newConfirmBtn.addEventListener('click', async () => {
        const splitterSelect = document.getElementById('reindex-splitter-type');
        const splitterType = splitterSelect ? splitterSelect.value : 'ast';
        
        try {
            const response = await fetch(`${API_BASE}/projects/${encodedPath}/reindex`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    splitter: splitterType
                })
            });
            
            const data = await response.json();
            
            if (!response.ok || !data.success) {
                throw new Error(data.error || '重新索引失败');
            }
            
            // 显示成功消息
            showSuccess(data.message);
            
            // 刷新任务和项目列表
            loadTasks();
            loadProjects();
            
        } catch (error) {
            console.error('重新索引项目失败:', error);
            showError('重新索引项目失败: ' + error.message);
        } finally {
            hideModal();
        }
    });
    
    // 显示模态对话框
    modal.classList.add('show');
}

// 页签切换功能
function switchTab(tabName) {
    // 移除所有活跃状态
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelectorAll('.tab-pane').forEach(pane => {
        pane.classList.remove('active');
    });
    
    // 添加当前页签的活跃状态
    const currentTabBtn = document.querySelector(`[data-tab="${tabName}"]`);
    const currentTabPane = document.getElementById(`${tabName}-pane`);
    
    if (currentTabBtn && currentTabPane) {
        currentTabBtn.classList.add('active');
        currentTabPane.classList.add('active');
        
        // 根据切换的页签自动加载对应数据
        if (tabName === 'tasks') {
            loadTasks();
        } else if (tabName === 'projects') {
            loadProjects();
        }
    }
}

// 更新页签徽章数字
function updateTabBadges(tasksCount = 0, projectsCount = 0) {
    const tasksBadge = document.getElementById('tasks-badge');
    const projectsBadge = document.getElementById('projects-badge');
    
    if (tasksBadge) {
        tasksBadge.textContent = tasksCount;
        // 如果数量为0，隐藏徽章
        tasksBadge.style.display = tasksCount > 0 ? 'flex' : 'none';
    }
    
    if (projectsBadge) {
        projectsBadge.textContent = projectsCount;
        // 如果数量为0，隐藏徽章
        projectsBadge.style.display = projectsCount > 0 ? 'flex' : 'none';
    }
}

console.log('📱 前端脚本加载完成');