<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Context 索引管理器</title>
    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app">
        <!-- 顶部导航栏 -->
        <header class="header">
            <div class="header-content">
                <div class="brand">
                    <div class="brand-icon">🔍</div>
                    <h1 class="brand-title">Code Context</h1>
                    <span class="brand-subtitle">智能代码索引管理器</span>
                </div>
                <div class="header-status">
                    <div class="status-badge online" id="status-badge">
                        <div class="status-dot"></div>
                        <span id="server-status">服务器在线</span>
                    </div>
                    <button class="header-btn" onclick="location.reload()" title="刷新页面">
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M23 4v6h-6M1 20v-6h6"/>
                            <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"/>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main">
            <div class="container">
                <!-- 系统概览卡片 -->
                <section class="overview-section">
                    <div class="metrics-grid">
                        <div class="metric-card primary">
                            <div class="metric-icon">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
                                </svg>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value" id="active-tasks">0</div>
                                <div class="metric-label">活跃任务</div>
                            </div>
                        </div>
                        
                        <div class="metric-card success">
                            <div class="metric-icon">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M22 19a2 2 0 01-2 2H4a2 2 0 01-2-2V5a2 2 0 012-2h5l2 3h9a2 2 0 012 2z"/>
                                </svg>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value" id="indexed-codebases">0</div>
                                <div class="metric-label">已索引项目</div>
                            </div>
                        </div>
                        
                        <div class="metric-card info">
                            <div class="metric-icon">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <circle cx="12" cy="12" r="10"/>
                                    <polyline points="12,6 12,12 16,14"/>
                                </svg>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value" id="last-update">-</div>
                                <div class="metric-label">最后更新</div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 添加新项目区域 -->
                <section class="add-project-section">
                    <div class="section-header">
                        <div class="section-title">
                            <svg class="section-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <circle cx="12" cy="12" r="10"/>
                                <line x1="12" y1="8" x2="12" y2="16"/>
                                <line x1="8" y1="12" x2="16" y2="12"/>
                            </svg>
                            <h2>添加新项目</h2>
                        </div>
                    </div>
                    
                    <div class="form-card">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="project-path" class="form-label">
                                    <svg class="form-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M22 19a2 2 0 01-2 2H4a2 2 0 01-2-2V5a2 2 0 012-2h5l2 3h9a2 2 0 012 2z"/>
                                    </svg>
                                    项目目录路径
                                </label>
                                <div class="input-group">
                                    <input type="text" id="project-path" class="form-input" 
                                           placeholder="请输入项目目录的完整路径，例如: /Users/<USER>/my-project" 
                                           autocomplete="off">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="splitter-type" class="form-label">
                                    <svg class="form-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <polyline points="16,18 22,12 16,6"/>
                                        <polyline points="8,6 2,12 8,18"/>
                                    </svg>
                                    分词器类型
                                </label>
                                <select id="splitter-type" class="form-select">
                                    <option value="ast">AST (推荐)</option>
                                    <option value="langchain">LangChain</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button class="btn btn-primary" onclick="addNewProject()" type="button">
                                <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
                                </svg>
                                开始索引
                            </button>
                        </div>
                    </div>
                </section>

                <!-- 页签区域 -->
                <section class="tabs-section">
                    <div class="tabs-container">
                        <!-- 页签导航 -->
                        <div class="tabs-nav">
                            <button class="tab-btn active" data-tab="tasks" onclick="switchTab('tasks')">
                                <svg class="tab-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z"/>
                                    <polyline points="14,2 14,8 20,8"/>
                                    <line x1="16" y1="13" x2="8" y2="13"/>
                                    <line x1="16" y1="17" x2="8" y2="17"/>
                                    <polyline points="10,9 9,9 8,9"/>
                                </svg>
                                <span>索引任务</span>
                                <div class="tab-badge" id="tasks-badge">0</div>
                            </button>
                            <button class="tab-btn" data-tab="projects" onclick="switchTab('projects')">
                                <svg class="tab-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M2 3h6a4 4 0 014 4v14a3 3 0 00-3-3H2z"/>
                                    <path d="M22 3h-6a4 4 0 00-4 4v14a3 3 0 013-3h7z"/>
                                </svg>
                                <span>已索引项目</span>
                                <div class="tab-badge" id="projects-badge">0</div>
                            </button>
                        </div>

                        <!-- 页签内容 -->
                        <div class="tabs-content">
                            <!-- 索引任务页签内容 -->
                            <div class="tab-pane active" id="tasks-pane">
                                <div class="tab-header">
                                    <h3>索引任务管理</h3>
                                    <button class="section-btn" onclick="loadTasks()">
                                        <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <path d="M23 4v6h-6M1 20v-6h6"/>
                                            <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"/>
                                        </svg>
                                        刷新
                                    </button>
                                </div>
                                
                                <div class="content-card">
                                    <div class="tasks-container" id="tasks-container">
                                        <div class="loading-state" id="loading">
                                            <div class="loading-spinner"></div>
                                            <span class="loading-text">加载任务中...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 已索引项目页签内容 -->
                            <div class="tab-pane" id="projects-pane">
                                <div class="tab-header">
                                    <h3>已索引项目</h3>
                                    <button class="section-btn" onclick="loadProjects()">
                                        <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <path d="M23 4v6h-6M1 20v-6h6"/>
                                            <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"/>
                                        </svg>
                                        刷新
                                    </button>
                                </div>
                                
                                <div class="content-card">
                                    <div class="projects-container" id="projects-container">
                                        <div class="loading-state" id="projects-loading">
                                            <div class="loading-spinner"></div>
                                            <span class="loading-text">加载项目中...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </main>

        <!-- 页脚 -->
        <footer class="footer">
            <div class="footer-content">
                <p>Code Context MCP Server - 实时索引管理界面</p>
                <div class="footer-links">
                    <a href="#" onclick="location.reload()">刷新</a>
                    <span class="divider">•</span>
                    <span>版本 v0.0.7</span>
                </div>
            </div>
        </footer>
    </div>

    <!-- 模态对话框 -->
    <div class="modal-overlay" id="confirmation-modal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title" id="modal-title">确认操作</h3>
                <button class="modal-close" onclick="hideModal()">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                </button>
            </div>
            <div class="modal-content">
                <div id="modal-message">确定要执行此操作吗？</div>
            </div>
            <div class="modal-actions">
                <button class="btn btn-secondary" onclick="hideModal()">取消</button>
                <button class="btn btn-primary" id="confirm-btn">确认</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>