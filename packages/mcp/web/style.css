/* ===== 全局样式重置 ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    height: 100%;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1f1f1f 100%);
    background-attachment: fixed;
    min-height: 100vh;
    color: #e5e7eb;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===== 主应用容器 ===== */
.app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* ===== 头部导航 ===== */
.header {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.brand {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.brand-icon {
    font-size: 2rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.brand-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: white;
    letter-spacing: -0.025em;
}

.brand-subtitle {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 400;
}

.header-status {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.status-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.2);
    border-radius: 2rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.status-badge.online {
    color: #10b981;
}

.status-badge.offline {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.header-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    padding: 0.5rem;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.header-btn .icon {
    width: 18px;
    height: 18px;
    stroke-width: 2;
}

/* ===== 主内容区域 ===== */
.main {
    flex: 1;
    padding: 2rem 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* ===== 区域样式 ===== */
section {
    margin-bottom: 2rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.section-title h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: white;
    letter-spacing: -0.025em;
}

.section-icon {
    width: 24px;
    height: 24px;
    stroke: white;
    stroke-width: 2;
}

.section-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    color: white;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.section-btn .btn-icon {
    width: 16px;
    height: 16px;
    stroke-width: 2;
}

/* ===== 卡片样式 ===== */
.content-card,
.form-card {
    background: rgba(45, 45, 45, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(75, 85, 99, 0.3);
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.content-card:hover,
.form-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

/* ===== 指标卡片 ===== */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.metric-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 1rem;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
}

.metric-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.metric-card.primary {
    border-left: 4px solid #667eea;
}

.metric-card.primary .metric-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.metric-card.success {
    border-left: 4px solid #10b981;
}

.metric-card.success .metric-icon {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.metric-card.info {
    border-left: 4px solid #06b6d4;
}

.metric-card.info .metric-icon {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
    color: white;
}

.metric-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.metric-icon svg {
    width: 24px;
    height: 24px;
    stroke-width: 2;
}

.metric-content {
    flex: 1;
}

.metric-value {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.metric-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

/* ===== 表单样式 ===== */
.form-grid {
    display: grid;
    grid-template-columns: 1fr 200px;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
}

.form-icon {
    width: 16px;
    height: 16px;
    stroke-width: 2;
    color: #6b7280;
}

.input-group {
    display: flex;
    border: 2px solid rgba(75, 85, 99, 0.5);
    border-radius: 0.75rem;
    overflow: hidden;
    transition: all 0.2s ease;
    background: rgba(31, 41, 55, 0.8);
}

.input-group:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.form-input {
    flex: 1;
    padding: 0.875rem 1rem;
    border: none;
    outline: none;
    font-size: 0.875rem;
    background: transparent;
    color: #f3f4f6;
}

.form-input::placeholder {
    color: #6b7280;
}

.input-btn {
    background: #f8fafc;
    border: none;
    border-left: 1px solid #e5e7eb;
    padding: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #6b7280;
    display: flex;
    align-items: center;
    justify-content: center;
}

.input-btn:hover {
    background: #f1f5f9;
    color: #374151;
}

.input-btn svg {
    width: 18px;
    height: 18px;
    stroke-width: 2;
}

.form-select {
    padding: 0.875rem 1rem;
    border: 2px solid rgba(75, 85, 99, 0.5);
    border-radius: 0.75rem;
    font-size: 0.875rem;
    background: rgba(31, 41, 55, 0.8);
    color: #f3f4f6;
    cursor: pointer;
    transition: all 0.2s ease;
}

.form-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
}

/* ===== 按钮样式 ===== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 1.5rem;
    border: none;
    border-radius: 0.75rem;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    min-height: 44px;
}

.btn .btn-icon {
    width: 18px;
    height: 18px;
    stroke-width: 2;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 14px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #f8fafc;
    color: #374151;
    border: 2px solid #e5e7eb;
}

.btn-secondary:hover {
    background: #f1f5f9;
    border-color: #d1d5db;
    transform: translateY(-1px);
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    box-shadow: 0 4px 14px rgba(239, 68, 68, 0.3);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

.btn-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    box-shadow: 0 4px 14px rgba(245, 158, 11, 0.3);
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* ===== 加载状态 ===== */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    color: #6b7280;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #e5e7eb;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 0.875rem;
    font-weight: 500;
}

/* ===== 空状态 ===== */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: #6b7280;
}

.empty-state-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

.empty-state p {
    font-size: 0.875rem;
    line-height: 1.6;
}

/* ===== 任务项样式 ===== */
.task-item {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.2s ease;
}

.task-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.task-path {
    font-weight: 600;
    color: #1f2937;
    font-size: 1rem;
}

.task-status {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.task-status.running {
    background: #fef3c7;
    color: #92400e;
}

.task-status.completed {
    background: #d1fae5;
    color: #065f46;
}

.task-status.error {
    background: #fee2e2;
    color: #991b1b;
}

.task-progress {
    margin-bottom: 1rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #f3f4f6;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.task-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.75rem;
    color: #6b7280;
}

.task-meta span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* ===== 项目项样式 ===== */
.project-item {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.2s ease;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.project-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.project-info {
    flex: 1;
}

.project-path {
    font-weight: 600;
    color: #1f2937;
    font-size: 1rem;
    margin-bottom: 0.75rem;
}

.project-stats {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 0.75rem;
    flex-wrap: wrap;
}

.project-stat {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.project-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.75rem;
    color: #9ca3af;
    flex-wrap: wrap;
}

.project-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
    margin-left: 1rem;
}

.btn-icon {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.btn-icon.primary {
    background: #f0f9ff;
    color: #0284c7;
    border: 1px solid #e0f2fe;
}

.btn-icon.primary:hover {
    background: #0284c7;
    color: white;
    transform: translateY(-1px);
}

.btn-icon.danger {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.btn-icon.danger:hover {
    background: #dc2626;
    color: white;
    transform: translateY(-1px);
}

.btn-icon.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.btn-icon.disabled:hover {
    transform: none;
    background: inherit !important;
    color: inherit !important;
}

.error-message {
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 0.5rem;
    padding: 0.75rem;
    margin-top: 0.75rem;
    font-size: 0.875rem;
    color: #991b1b;
}

/* ===== 模态框样式 ===== */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: white;
    border-radius: 1rem;
    padding: 0;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    transform: scale(0.9) translateY(20px);
    transition: all 0.3s ease;
}

.modal-overlay.show .modal {
    transform: scale(1) translateY(0);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
}

.modal-close {
    background: none;
    border: none;
    padding: 0.5rem;
    cursor: pointer;
    color: #6b7280;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #f3f4f6;
    color: #374151;
}

.modal-close svg {
    width: 20px;
    height: 20px;
    stroke-width: 2;
}

.modal-content {
    padding: 1.5rem;
    font-size: 0.875rem;
    line-height: 1.6;
    color: #6b7280;
}

.modal-actions {
    padding: 1.5rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* ===== 页脚样式 ===== */
.footer {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    margin-top: auto;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.8);
}

.footer-links {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.2s ease;
}

.footer-links a:hover {
    color: white;
}

.divider {
    opacity: 0.5;
}

/* ===== 通知系统 ===== */
.notification {
    position: fixed;
    top: 1rem;
    right: 1rem;
    background: white;
    border-radius: 0.75rem;
    padding: 1rem 1.5rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    z-index: 1100;
    transform: translateX(400px);
    transition: all 0.3s ease;
    border-left: 4px solid #10b981;
    max-width: 400px;
}

.notification.show {
    transform: translateX(0);
}

.notification.error {
    border-left-color: #ef4444;
}

.notification.warning {
    border-left-color: #f59e0b;
}

.notification.info {
    border-left-color: #06b6d4;
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .header-content {
        padding: 1rem;
    }
    
    .brand-title {
        font-size: 1.5rem;
    }
    
    .brand-subtitle {
        display: none;
    }
    
    .container {
        padding: 0 1rem;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .project-item {
        flex-direction: column;
        gap: 1rem;
    }
    
    .project-actions {
        margin-left: 0;
        align-self: flex-end;
    }
    
    .project-stats {
        gap: 1rem;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .main {
        padding: 1rem 0;
    }
    
    .content-card,
    .form-card {
        padding: 1.5rem;
        border-radius: 0.75rem;
    }
    
    .metric-card {
        padding: 1rem;
    }
    
    .project-stats {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .modal {
        width: 95%;
    }
}

/* ===== 辅助类 ===== */
.hidden {
    display: none !important;
}

.fade-in {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-out {
    animation: fadeOut 0.3s ease forwards;
}

@keyframes fadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(-10px); }
}

/* ===== 滚动条样式 ===== */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* ===== 选择文本样式 ===== */
::selection {
    background: rgba(102, 126, 234, 0.2);
    color: #1f2937;
}

::-moz-selection {
    background: rgba(102, 126, 234, 0.2);
    color: #1f2937;
}

/* ===== 任务卡片样式 ===== */
.task-card {
    background: rgba(55, 65, 81, 0.9);
    border: 1px solid rgba(75, 85, 99, 0.4);
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.task-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    opacity: 0.8;
}

.task-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
    border-color: #6b7280;
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    gap: 1rem;
}

.task-info {
    flex: 1;
    min-width: 0;
}

.task-info h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #f3f4f6;
    margin: 0 0 0.5rem 0;
    word-break: break-all;
    line-height: 1.4;
}

.task-id {
    font-size: 0.75rem;
    color: #9ca3af;
    font-family: 'Monaco', 'Consolas', monospace;
    background: rgba(31, 41, 55, 0.6);
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    display: inline-block;
}

.task-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-shrink: 0;
    flex-wrap: wrap;
}

/* ===== 状态徽章样式 ===== */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.2s ease;
    border: 2px solid;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

.status-badge .status-icon {
    width: 16px;
    height: 16px;
    stroke-width: 2.5;
}

.status-badge.status-indexing {
    color: #0ea5e9;
    border-color: #0ea5e9;
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.1), rgba(6, 182, 212, 0.15));
    animation: pulse-indexing 2s infinite;
}

.status-badge.status-paused {
    color: #f59e0b;
    border-color: #f59e0b;
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(217, 119, 6, 0.15));
}

.status-badge.status-completed {
    color: #10b981;
    border-color: #10b981;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.15));
}

.status-badge.status-error {
    color: #ef4444;
    border-color: #ef4444;
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.15));
}

.status-badge.status-cancelling {
    color: #8b5cf6;
    border-color: #8b5cf6;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(124, 58, 237, 0.15));
    animation: pulse-cancelling 1.5s infinite;
}

@keyframes pulse-indexing {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(14, 165, 233, 0.4);
    }
    50% {
        transform: scale(1.02);
        box-shadow: 0 0 0 8px rgba(14, 165, 233, 0);
    }
}

@keyframes pulse-cancelling {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(0.98);
        opacity: 0.8;
    }
}

/* ===== 按钮组优化 ===== */
.task-actions .btn {
    padding: 0.625rem 1.25rem;
    font-size: 0.8125rem;
    min-height: 36px;
    border-radius: 0.625rem;
}

.task-actions .btn:first-of-type {
    margin-right: 0.25rem;
}

/* ===== 进度条样式优化 ===== */
.progress-section {
    margin-bottom: 1.25rem;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
    color: #d1d5db;
    font-weight: 500;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(31, 41, 55, 0.7);
    border-radius: 4px;
    overflow: hidden;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.3);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #0ea5e9, #06b6d4);
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* ===== 项目统计图标样式 ===== */
.stat-icon {
    width: 16px;
    height: 16px;
    stroke-width: 2;
    color: #9ca3af;
    margin-right: 0.25rem;
}

.project-stat.status-completed .stat-icon {
    color: #10b981;
}

.project-stat.status-indexing .stat-icon {
    color: #0ea5e9;
}

.project-stat.status-error .stat-icon {
    color: #ef4444;
}

/* ===== 项目卡片深色主题 ===== */
.project-item {
    background: rgba(55, 65, 81, 0.9);
    border: 1px solid rgba(75, 85, 99, 0.4);
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.project-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
    border-color: #6b7280;
}

.project-path {
    font-weight: 600;
    color: #f3f4f6;
    font-size: 1rem;
    margin-bottom: 1rem;
    word-break: break-all;
}

.project-stat {
    color: #d1d5db;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
}

.project-meta {
    color: #9ca3af;
    font-size: 0.75rem;
    margin-top: 0.75rem;
}

.project-meta span {
    margin-right: 1rem;
}

/* ===== 页签系统样式 ===== */
.tabs-section {
    margin-bottom: 2rem;
}

.tabs-container {
    background: rgba(45, 45, 45, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(75, 85, 99, 0.3);
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.tabs-nav {
    display: flex;
    background: rgba(31, 41, 55, 0.8);
    border-bottom: 1px solid rgba(75, 85, 99, 0.3);
    padding: 0;
}

.tab-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 1.25rem 2rem;
    background: transparent;
    border: none;
    color: #9ca3af;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    border-right: 1px solid rgba(75, 85, 99, 0.3);
}

.tab-btn:last-child {
    border-right: none;
}

.tab-btn:hover {
    background: rgba(55, 65, 81, 0.6);
    color: #d1d5db;
}

.tab-btn.active {
    background: rgba(45, 45, 45, 0.8);
    color: #f3f4f6;
    border-bottom: 3px solid #667eea;
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 2px 2px 0 0;
}

.tab-icon {
    width: 18px;
    height: 18px;
    stroke-width: 2;
    transition: transform 0.2s ease;
}

.tab-btn:hover .tab-icon {
    transform: scale(1.1);
}

.tab-badge {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    min-width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.tab-btn:hover .tab-badge {
    transform: scale(1.1);
}

.tabs-content {
    padding: 0;
}

.tab-pane {
    display: none;
    padding: 2rem;
}

.tab-pane.active {
    display: block;
}

.tab-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(75, 85, 99, 0.3);
}

.tab-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #f3f4f6;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tab-header h3::before {
    content: '';
    width: 4px;
    height: 20px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 2px;
}

/* ===== 页签响应式设计 ===== */
@media (max-width: 768px) {
    .tab-btn {
        padding: 1rem 1.5rem;
        font-size: 0.8125rem;
    }
    
    .tab-btn span {
        display: none;
    }
    
    .tab-icon {
        width: 20px;
        height: 20px;
    }
    
    .tab-pane {
        padding: 1.5rem;
    }
    
    .tab-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
}

@media (max-width: 480px) {
    .tab-btn {
        padding: 0.875rem 1rem;
    }
    
    .tab-badge {
        font-size: 0.6875rem;
        padding: 0.125rem 0.375rem;
        min-width: 1.25rem;
        height: 1.25rem;
    }
}