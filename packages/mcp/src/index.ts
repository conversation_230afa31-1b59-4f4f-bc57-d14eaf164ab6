#!/usr/bin/env node

// CRITICAL: Redirect console outputs to stderr IMMEDIATELY to avoid interfering with MCP JSON protocol
// Only MCP protocol messages should go to stdout
const originalConsoleLog = console.log;
const originalConsoleWarn = console.warn;

console.log = (...args: any[]) => {
    process.stderr.write('[LOG] ' + args.join(' ') + '\n');
};

console.warn = (...args: any[]) => {
    process.stderr.write('[WARN] ' + args.join(' ') + '\n');
};

// console.error already goes to stderr by default

import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
    ListToolsRequestSchema,
    CallToolRequestSchema
} from "@modelcontextprotocol/sdk/types.js";
import { CodeContext } from "@zilliz/code-context-core";
import { MilvusVectorDatabase } from "@zilliz/code-context-core";
import express from "express";
import cors from "cors";
import * as path from "path";
import * as os from "os";
import * as fs from "fs";
import * as crypto from "crypto";
import { fileURLToPath } from "url";

// Import our modular components
import { createMcpConfig, logConfigurationSummary, showHelpMessage, CodeContextMcpConfig, IndexedProject, IndexingTask, CodebaseSnapshot } from "./config.js";
import { createEmbeddingInstance, logEmbeddingProviderInfo } from "./embedding.js";
import { SnapshotManager } from "./snapshot.js";
import { SyncManager } from "./sync.js";
import { ToolHandlers } from "./handlers.js";

// ES module compatibility: get the current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Interfaces are now imported from config.ts to ensure consistency

class CodeContextMcpServer {
    private server: Server;
    private codeContext: CodeContext;
    private snapshotManager: SnapshotManager;
    private syncManager: SyncManager;
    private toolHandlers: ToolHandlers;
    
    // Task and project management - 恢复到合并前本地的完整逻辑
    private activeCodebasePath: string | null = null;
    private indexedCodebases: string[] = []; // Keep for backward compatibility
    private indexedProjects: IndexedProject[] = []; // New detailed project information
    private indexingTasks: Map<string, IndexingTask> = new Map(); // Key is taskId (optimized for O(1) lookup)
    private pathToTaskIdMap: Map<string, string> = new Map(); // Key is codebasePath, value is taskId
    private indexingStats: { indexedFiles: number; totalChunks: number } | null = null;
    private isSyncing: boolean = false;
    private snapshotFilePath: string;
    private currentWorkspace: string;
    
    // Snapshot throttling for I/O optimization
    private snapshotSaveTimeout: NodeJS.Timeout | null = null;
    private snapshotSavePromise: Promise<void> | null = null;
    private pendingSnapshotSave: boolean = false;
    
    // Express web server for external API
    private webApp!: express.Application;
    private webServer: any;
    private webPort: number = 3001; // Default port

    constructor(config: CodeContextMcpConfig) {
        // Get current workspace
        this.currentWorkspace = process.cwd();
        console.log(`[WORKSPACE] Current workspace: ${this.currentWorkspace}`);

        // Initialize snapshot file path
        this.snapshotFilePath = path.join(os.homedir(), '.codecontext', 'mcp-codebase-snapshot.json');

        // Initialize MCP server
        this.server = new Server(
            {
                name: config.name,
                version: config.version
            },
            {
                capabilities: {
                    tools: {}
                }
            }
        );

        // Initialize embedding provider
        console.log(`[EMBEDDING] Initializing embedding provider: ${config.embeddingProvider}`);
        console.log(`[EMBEDDING] Using model: ${config.embeddingModel}`);

        const embedding = createEmbeddingInstance(config);
        logEmbeddingProviderInfo(config, embedding);

        // Initialize vector database
        const vectorDatabase = new MilvusVectorDatabase({
            address: config.milvusAddress,
            ...(config.milvusToken && { token: config.milvusToken })
        });

        // Initialize code context
        this.codeContext = new CodeContext({
            embedding,
            vectorDatabase
        });

        // Initialize managers
        this.snapshotManager = new SnapshotManager();
        
        // CRITICAL: Load snapshot data into SnapshotManager BEFORE using it
        this.snapshotManager.loadCodebaseSnapshot();
        
        // Fix any data consistency issues in completed tasks
        this.snapshotManager.fixCompletedTasksData();
        
        this.syncManager = new SyncManager(this.codeContext, this.snapshotManager, this.indexingTasks);
        this.toolHandlers = new ToolHandlers(this.codeContext, this.snapshotManager, this.syncManager, this.indexingTasks);

        // Load existing codebase snapshot on startup (MCPServer's own loading)
        this.loadCodebaseSnapshot();
        
        // Build task lookup map from loaded data
        this.buildTaskLookupMap();

        this.setupTools();
        
        // Initialize Express web server for external API
        this.setupWebServer();
    }

    private setupTools() {
        // Get current working directory to provide to LLM
        const currentWorkingDirectory = process.cwd();

        const index_description = `
Index a codebase directory to enable semantic search using a configurable code splitter.

⚠️ **IMPORTANT**:
- You MUST provide an absolute path to the target codebase.
- Relative paths will be automatically resolved to absolute paths.
- Current working directory: ${currentWorkingDirectory}.
    You MUST use this directly and DO NOT append any subfolder.

✨ **Usage Guidance**:
- This tool is typically used when search fails due to an unindexed codebase.
- If indexing is attempted on an already indexed path, and a conflict is detected, you MUST prompt the user to confirm whether to proceed with a force index (i.e., re-indexing and overwriting the previous index).
`;


        const search_description = `
Search the indexed codebase using natural language queries within a specified absolute path.

⚠️ **IMPORTANT**:
- You MUST provide an absolute path.
- The current working directory is: ${currentWorkingDirectory}.
- You MUST use this as the default path and DO NOT append any subfolder.

✨ **Usage Guidance**:
- If the codebase is not indexed, this tool will return a clear error message indicating that indexing is required first.
- You can then use the index_codebase tool to index the codebase before searching again.
`;

        // Define available tools
        this.server.setRequestHandler(ListToolsRequestSchema, async () => {
            return {
                tools: [
                    {
                        name: "index_codebase",
                        description: index_description,
                        inputSchema: {
                            type: "object",
                            properties: {
                                path: {
                                    type: "string",
                                    description: `ABSOLUTE path to the codebase directory to index. Current working directory is: ${currentWorkingDirectory}. You can use this path directly or adjust as needed.`
                                },
                                force: {
                                    type: "boolean",
                                    description: "Force re-indexing even if already indexed",
                                    default: false
                                },
                                splitter: {
                                    type: "string",
                                    description: "Code splitter to use: 'ast' for syntax-aware splitting with automatic fallback, 'langchain' for character-based splitting",
                                    enum: ["ast", "langchain"],
                                    default: "ast"
                                },
                                customExtensions: {
                                    type: "array",
                                    items: {
                                        type: "string"
                                    },
                                    description: "Optional: Additional file extensions to include beyond defaults (e.g., ['.vue', '.svelte', '.astro']). Extensions should include the dot prefix or will be automatically added",
                                    default: []
                                },
                                ignorePatterns: {
                                    type: "array",
                                    items: {
                                        type: "string"
                                    },
                                    description: "Optional: Additional ignore patterns to exclude specific files/directories beyond defaults. Only include this parameter if the user explicitly requests custom ignore patterns (e.g., ['static/**', '*.tmp', 'private/**'])",
                                    default: []
                                }
                            },
                            required: ["path"]
                        }
                    },
                    {
                        name: "search_code",
                        description: search_description,
                        inputSchema: {
                            type: "object",
                            properties: {
                                path: {
                                    type: "string",
                                    description: `ABSOLUTE path to the codebase directory to search in. Current working directory is: ${currentWorkingDirectory}. You can use this path directly or adjust as needed.`
                                },
                                query: {
                                    type: "string",
                                    description: "Natural language query to search for in the codebase"
                                },
                                limit: {
                                    type: "number",
                                    description: "Maximum number of results to return",
                                    default: 10,
                                    maximum: 50
                                }
                            },
                            required: ["path", "query"]
                        }
                    },
                    {
                        name: "clear_index",
                        description: `Clear the search index. IMPORTANT: You MUST provide an absolute path. Current working directory is: ${currentWorkingDirectory}. You can use this as the default path or adjust as needed (e.g., ${currentWorkingDirectory}/subfolder).`,
                        inputSchema: {
                            type: "object",
                            properties: {
                                path: {
                                    type: "string",
                                    description: `ABSOLUTE path to the codebase directory to clear. Current working directory is: ${currentWorkingDirectory}. You can use this path directly or adjust as needed.`
                                }
                            },
                            required: ["path"]
                        }
                    },
                ]
            };
        });

        // Handle tool execution
        this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
            const { name, arguments: args } = request.params;

            switch (name) {
                case "index_codebase":
                    return await this.toolHandlers.handleIndexCodebase(args);
                case "search_code":
                    return await this.toolHandlers.handleSearchCode(args);
                case "clear_index":
                    return await this.toolHandlers.handleClearIndex(args);

                default:
                    throw new Error(`Unknown tool: ${name}`);
            }
        });
    }

    /**
     * Setup Express web server for external task management API
     */
    private setupWebServer() {
        this.webApp = express();
        
        // Middleware
        this.webApp.use(cors());
        this.webApp.use(express.json());
        
        // Add request logging
        this.webApp.use((req, _res, next) => {
            console.log(`[WEB-API] ${req.method} ${req.path}${req.query ? ' ' + JSON.stringify(req.query) : ''}`);
            next();
        });
        
        // Serve static files for web interface
        const staticPath = path.join(__dirname, '../web');
        this.webApp.use('/web', express.static(staticPath));
        
        // Redirect root to web interface
        this.webApp.get('/', (_req, res) => {
            res.redirect('/web');
        });
        
        // API Routes
        this.setupWebRoutes();
        
        // Start server
        const port = parseInt(process.env.WEB_PORT || '3001', 10);
        this.webPort = port;
        
        this.webServer = this.webApp.listen(port, () => {
            console.log(`[WEB-API] 🌐 Web API server started on http://localhost:${port}`);
            console.log(`[WEB-API] Web interface available at: http://localhost:${port}/web`); 
            console.log(`[WEB-API] Available endpoints:`);
            console.log(`[WEB-API]   GET    /api/tasks           - Get all indexing tasks`);
            console.log(`[WEB-API]   GET    /api/tasks/:taskId   - Get specific task status`);
            console.log(`[WEB-API]   POST   /api/tasks/:taskId/pause  - Pause indexing task`);
            console.log(`[WEB-API]   POST   /api/tasks/:taskId/resume - Resume indexing task`);
            console.log(`[WEB-API]   DELETE /api/tasks/:taskId       - Cancel/clear indexing task`);
        }).on('error', (error: any) => {
            if (error.code === 'EADDRINUSE') {
                console.warn(`[WEB-API] ⚠️  Port ${port} is in use. Web API server will not be available.`);
            } else {
                console.error(`[WEB-API] ❌ Failed to start web server:`, error);
            }
        });
    }

    /**
     * Setup API routes for the web server
     */
    private setupWebRoutes() {
        // Get all indexing tasks
        this.webApp.get('/api/tasks', (req, res) => {
            try {
                // Use SnapshotManager to get active tasks
                const activeTasks = this.snapshotManager.getActiveTasks();
                const tasks = activeTasks.map(task => ({
                    taskId: task.taskId,
                    path: task.path,
                    status: task.status,
                    progress: {
                        processedFiles: task.processedFiles || 0,
                        totalFiles: task.totalFiles || 0,
                        percentage: task.totalFiles && task.totalFiles > 0 ? 
                            ((task.processedFiles || 0) / task.totalFiles * 100).toFixed(2) + '%' : '0%'
                    },
                    phase: task.phase || 'Unknown',
                    startTime: task.startTime,
                    endTime: task.endTime,
                    lastError: task.lastError || null,
                    // 添加前端期望的字段
                    totalChunks: task.totalChunks || 0,
                    splitter: task.splitter || 'ast',
                    forceReindex: task.forceReindex || false
                }));

                res.json({
                    success: true,
                    data: {
                        tasks,
                        totalTasks: tasks.length,
                        indexedCodebases: this.snapshotManager.getIndexedProjects().length
                    }
                });
            } catch (error: any) {
                console.error(`[WEB-API] Error getting tasks:`, error);
                res.status(500).json({
                    success: false,
                    error: error.message || 'Internal server error'
                });
            }
        });

        // Get specific task status
        this.webApp.get('/api/tasks/:taskId', (req, res) => {
            try {
                const { taskId } = req.params;
                const task = this.snapshotManager.getTask(taskId);
                
                if (!task) {
                    return res.status(404).json({
                        success: false,
                        error: `Task with ID '${taskId}' not found`
                    });
                }

                res.json({
                    success: true,
                    data: {
                        taskId: task.taskId,
                        path: task.path,
                        status: task.status,
                        progress: {
                            processedFiles: task.processedFiles || 0,
                            totalFiles: task.totalFiles || 0,
                            percentage: task.totalFiles && task.totalFiles > 0 ? 
                                ((task.processedFiles || 0) / task.totalFiles * 100).toFixed(2) + '%' : '0%'
                        },
                        phase: task.phase || 'Unknown',
                        startTime: task.startTime,
                        endTime: task.endTime,
                        totalChunks: task.totalChunks || 0,
                        splitter: task.splitter || 'ast',
                        forceReindex: task.forceReindex || false,
                        lastError: task.lastError || null
                    }
                });
            } catch (error: any) {
                console.error(`[WEB-API] Error getting task:`, error);
                res.status(500).json({
                    success: false,
                    error: error.message || 'Internal server error'
                });
            }
        });

        // Pause indexing task
        this.webApp.post('/api/tasks/:taskId/pause', async (req, res) => {
            try {
                const { taskId } = req.params;
                const task = this.snapshotManager.getTask(taskId);
                
                if (!task) {
                    return res.status(404).json({
                        success: false,
                        error: `Task with ID '${taskId}' not found`
                    });
                }

                if (task.status !== 'indexing') {
                    return res.status(400).json({
                        success: false, 
                        error: `Task '${taskId}' is not currently running (status: ${task.status})`
                    });
                }
                
                // Pause the task - CRITICAL: Update both SnapshotManager AND memory tasks
                task.status = 'paused';
                
                // CRITICAL: Also update the task in memory indexingTasks to ensure state consistency
                const memoryTask = this.indexingTasks.get(taskId);
                if (memoryTask) {
                    memoryTask.status = 'paused';
                    console.log(`[WEB-API] Updated memory task status to 'paused' for ${taskId}`);
                } else {
                    console.warn(`[WEB-API] Memory task not found for ${taskId} during pause operation`);
                }
                
                // CRITICAL: Clear sync lock when user manually pauses task
                this.syncManager.clearSyncLock();
                
                this.saveCodebaseSnapshot();
                
                console.log(`[WEB-API] Task '${taskId}' paused via web API`);
                
                res.json({
                    success: true,
                    message: `Indexing paused for task '${taskId}' (${task.path})`
                });
            } catch (error: any) {
                console.error(`[WEB-API] Error pausing task:`, error);
                res.status(500).json({
                    success: false,
                    error: error.message || 'Internal server error'
                });
            }
        });

        // Resume indexing task
        this.webApp.post('/api/tasks/:taskId/resume', async (req, res) => {
            try {
                const { taskId } = req.params;
                const task = this.snapshotManager.getTask(taskId);
                
                if (!task) {
                    return res.status(404).json({
                        success: false,
                        error: `Task with ID '${taskId}' not found`
                    });
                }

                if (task.status !== 'paused' && task.status !== 'error') {
                    return res.status(400).json({
                        success: false,
                        error: `Task '${taskId}' cannot be resumed (status: ${task.status})`
                    });
                }

                const wasError = task.status === 'error';
                
                // Resume the task - CRITICAL: Update both SnapshotManager AND memory tasks
                task.status = 'indexing';
                task.lastError = undefined;
                
                // CRITICAL: Also update the task in memory indexingTasks to ensure state consistency
                const memoryTask = this.indexingTasks.get(taskId);
                if (memoryTask) {
                    memoryTask.status = 'indexing';
                    memoryTask.lastError = undefined;
                    console.log(`[WEB-API] Updated memory task status to 'indexing' for ${taskId}`);
                } else {
                    console.warn(`[WEB-API] Memory task not found for ${taskId}, adding to indexingTasks`);
                    // If memory task doesn't exist, add it based on SnapshotManager task
                    this.indexingTasks.set(taskId, { ...task });
                    this.pathToTaskIdMap.set(task.path, taskId);
                }
                
                this.saveCodebaseSnapshot();
                
                // Restart the background indexing process
                this.resumeBackgroundIndexing(task).catch(error => {
                    console.error(`[WEB-API] Failed to restart indexing for task ${taskId}:`, error);
                    // Update task status back to error if restart fails
                    const failedTask = this.snapshotManager.getTask(taskId);
                    if (failedTask) {
                        failedTask.status = 'error';
                        failedTask.lastError = error.message || error.toString();
                        this.saveCodebaseSnapshot();
                    }
                });
                
                console.log(`[WEB-API] Task '${taskId}' ${wasError ? 'retried' : 'resumed'} via web API`);
                
                const actionWord = wasError ? 'retried' : 'resumed';
                const statusMessage = wasError ? ' from error state' : '';
                
                res.json({
                    success: true,
                    message: `Indexing ${actionWord} for task '${taskId}' (${task.path})${statusMessage}. Progress: ${task.processedFiles}/${task.totalFiles} files completed`
                });
            } catch (error: any) {
                console.error(`[WEB-API] Error resuming task:`, error);
                res.status(500).json({
                    success: false,
                    error: error.message || 'Internal server error'
                });
            }
        });

        // Cancel/clear indexing task
        this.webApp.delete('/api/tasks/:taskId', async (req, res) => {
            try {
                const { taskId } = req.params;
                const task = this.snapshotManager.getTask(taskId);
                
                if (!task) {
                    return res.status(404).json({
                        success: false,
                        error: `Task with ID '${taskId}' not found`
                    });
                }
                
                const taskPath = task.path;
                
                // If task is actively indexing, initiate cancellation
                if (task.status === 'indexing') {
                    this.snapshotManager.updateTask(taskId, { status: 'cancelling' });
                    
                    // CRITICAL: Also update the task in memory indexingTasks to ensure state consistency
                    const memoryTask = this.indexingTasks.get(taskId);
                    if (memoryTask) {
                        memoryTask.status = 'cancelling';
                        console.log(`[WEB-API] Updated memory task status to 'cancelling' for ${taskId}`);
                    } else {
                        console.warn(`[WEB-API] Memory task not found for ${taskId} during cancellation`);
                    }
                    
                    // CRITICAL: Clear sync lock when user manually cancels task
                    this.syncManager.clearSyncLock();
                    
                    this.snapshotManager.saveCodebaseSnapshot();
                    console.log(`[WEB-API] Cancellation initiated for task '${taskId}' via web API (${taskPath})`);
                    return res.json({
                        success: true,
                        message: `Cancellation initiated for task '${taskId}'. It will be removed shortly.`
                    });
                }
                
                // For other statuses, clean up immediately
                try {
                    if (task.status !== 'completed') {
                        await this.toolHandlers.handleClearIndex({ path: taskPath });
                    }
                } catch (clearError) {
                    console.warn(`[WEB-API] Warning: Failed to clear index for task ${taskId}:`, clearError);
                }
                
                // CRITICAL: Clear sync lock when user manually deletes task
                this.syncManager.clearSyncLock();
                
                // Remove the task completely
                this.snapshotManager.removeTask(taskId);
                this.pathToTaskIdMap.delete(taskPath);
                
                // Remove from projects list if not completed
                if (task.status !== 'completed') {
                    this.snapshotManager.removeProject(taskPath);
                }
                
                this.snapshotManager.saveCodebaseSnapshot();
                console.log(`[WEB-API] Task '${taskId}' cleared via web API (${taskPath})`);
                
                res.json({
                    success: true,
                    message: `Indexing task '${taskId}' cleared successfully (${taskPath})`
                });
                
            } catch (error: any) {
                console.error(`[WEB-API] Error clearing/cancelling task:`, error);
                res.status(500).json({
                    success: false,
                    error: error.message || 'Internal server error'
                });
            }
        });

        // Basic health check endpoint
        this.webApp.get('/api/health', (_req, res) => {
            res.json({
                success: true,
                status: 'healthy',
                timestamp: new Date().toISOString(),
                server: 'CodeContext MCP Server',
                activeTasks: this.snapshotManager.getActiveTasks().length,
                indexedCodebases: this.snapshotManager.getIndexedCodebases().length
            });
        });

        // Get all indexed projects
        this.webApp.get('/api/projects', (req, res) => {
            try {
                console.log(`[WEB-API] GET /api/projects`, req.query);
                
                // Only return completed projects, not indexing ones
                const completedProjects = this.snapshotManager.getIndexedProjects().filter(p => p.status === 'completed');
                
                res.json({
                    success: true,
                    data: {
                        projects: completedProjects,
                        totalProjects: completedProjects.length
                    }
                });
            } catch (error: any) {
                console.error(`[WEB-API] Error getting projects:`, error);
                res.status(500).json({
                    success: false,
                    error: error.message || 'Internal server error'
                });
            }
        });

        // Clear index for a specific project  
        this.webApp.delete('/api/projects/:projectId', async (req, res) => {
            try {
                const { projectId } = req.params;
                console.log(`[WEB-API] DELETE /api/projects/${projectId}`);
                
                // Find project by collection name or path
                const project = this.snapshotManager.getIndexedProjects().find(p => 
                    p.collectionName === projectId || p.path === decodeURIComponent(projectId)
                );
                
                if (!project) {
                    return res.status(404).json({
                        success: false,
                        error: `Project with ID '${projectId}' not found`
                    });
                }
                
                // Clear the index
                await this.toolHandlers.handleClearIndex({ path: project.path });
                
                // Remove from projects list
                this.snapshotManager.removeProject(project.path);
                this.snapshotManager.removeIndexedCodebase(project.path);
                this.snapshotManager.saveCodebaseSnapshot();
                
                console.log(`[WEB-API] Project '${project.path}' index cleared successfully`);
                
                res.json({
                    success: true,
                    message: `Index cleared for project '${project.path}'`
                });
            } catch (error: any) {
                console.error(`[WEB-API] Error clearing project index:`, error);
                res.status(500).json({
                    success: false,
                    error: error.message || 'Internal server error'
                });
            }
        });

        // Reindex a specific project
        this.webApp.post('/api/projects/:projectId/reindex', async (req, res) => {
            try {
                const { projectId } = req.params;
                const { splitter = 'ast' } = req.body;
                console.log(`[WEB-API] POST /api/projects/${projectId}/reindex`, req.body);
                
                // Find project by collection name or path
                const project = this.snapshotManager.getIndexedProjects().find(p => 
                    p.collectionName === projectId || p.path === decodeURIComponent(projectId)
                );
                
                if (!project) {
                    return res.status(404).json({
                        success: false,
                        error: `Project with ID '${projectId}' not found`
                    });
                }

                // Start indexing with force=true
                await this.toolHandlers.handleIndexCodebase({ 
                    path: project.path, 
                    splitter: splitter,
                    force: true
                });
                
                res.json({
                    success: true,
                    message: `Reindexing started for project '${project.path}' using ${splitter.toUpperCase()} splitter`
                });
            } catch (error: any) {
                console.error(`[WEB-API] Error starting reindex:`, error);
                res.status(500).json({
                    success: false,
                    error: error.message || 'Internal server error'
                });
            }
        });

        // Add new project for indexing
        this.webApp.post('/api/projects', async (req, res) => {
            try {
                const { path: projectPath, splitter = 'ast' } = req.body;
                console.log(`[WEB-API] POST /api/projects`, req.body);
                
                if (!projectPath) {
                    return res.status(400).json({
                        success: false,
                        error: 'Project path is required'
                    });
                }

                const absolutePath = path.resolve(projectPath);
                
                // Start indexing
                await this.toolHandlers.handleIndexCodebase({ 
                    path: absolutePath, 
                    splitter: splitter,
                    force: false
                });
                
                res.json({
                    success: true,
                    message: `Indexing started for project '${absolutePath}' using ${splitter.toUpperCase()} splitter`
                });
            } catch (error: any) {
                console.error(`[WEB-API] Error adding project:`, error);
                res.status(500).json({
                    success: false,
                    error: error.message || 'Internal server error'
                });
            }
        });
    }

    async start() {
        console.log('[SYNC-DEBUG] MCP server start() method called');
        console.log('Starting CodeContext MCP server...');

        const transport = new StdioServerTransport();
        console.log('[SYNC-DEBUG] StdioServerTransport created, attempting server connection...');

        await this.server.connect(transport);
        console.log("MCP server started and listening on stdio.");
        console.log('[SYNC-DEBUG] Server connection established successfully');

        // Start background sync after server is connected
        console.log('[SYNC-DEBUG] Initializing background sync...');
        this.syncManager.startBackgroundSync();
        console.log('[SYNC-DEBUG] MCP server initialization complete');
    }

    /**
     * Build task lookup map from loaded data
     */
    private buildTaskLookupMap(): void {
        this.pathToTaskIdMap.clear();
        for (const task of this.snapshotManager.getActiveTasks()) {
            this.pathToTaskIdMap.set(task.path, task.taskId);
        }
        console.log(`[TASK-MAP] Built lookup map with ${this.pathToTaskIdMap.size} entries`);
    }


    /**
     * Load existing codebase snapshot from file
     */
    private loadCodebaseSnapshot(): void {
        console.log('[SNAPSHOT-DEBUG] Loading codebase snapshot from:', this.snapshotFilePath);

        try {
            if (!fs.existsSync(this.snapshotFilePath)) {
                console.log('[SNAPSHOT-DEBUG] Snapshot file does not exist. Starting fresh.');
                return;
            }

            const snapshotData = fs.readFileSync(this.snapshotFilePath, 'utf8');
            const snapshot: CodebaseSnapshot = JSON.parse(snapshotData);

            // Restore fully indexed codebases (backward compatibility)
            this.indexedCodebases = snapshot.indexedCodebases.filter(codebasePath => {
                const exists = fs.existsSync(codebasePath);
                if (!exists) console.warn(`[SNAPSHOT-DEBUG] Indexed codebase no longer exists: ${codebasePath}`);
                return exists;
            });

            // Restore detailed project information
            this.indexedProjects = (snapshot.indexedProjects || []).filter(project => {
                const exists = fs.existsSync(project.path);
                if (!exists) console.warn(`[SNAPSHOT-DEBUG] Indexed project no longer exists: ${project.path}`);
                return exists;
            });

            // Migrate from old format to new format if needed
            if (this.indexedCodebases.length > 0 && this.indexedProjects.length === 0) {
                console.log('[SNAPSHOT-DEBUG] Migrating old format to new project format...');
                this.indexedProjects = this.indexedCodebases.map(path => ({
                    path,
                    indexedAt: new Date().toISOString(),
                    totalFiles: 0,
                    totalChunks: 0,
                    splitter: 'ast' as const,
                    collectionName: this.generateCollectionName(path),
                    status: 'completed' as const
                }));
            }

            // Restore active/paused tasks with optimized data structure
            this.indexingTasks.clear();
            this.pathToTaskIdMap.clear();
            
            for (const task of snapshot.activeTasks || []) {
                if (fs.existsSync(task.path)) {
                    // CRITICAL: If server restarted while task was running, set to 'paused'
                    if (task.status === 'indexing') {
                        console.warn(`[SNAPSHOT-DEBUG] Found interrupted indexing task for ${task.path}. Setting status to 'paused'.`);
                        task.status = 'paused';
                    }
                    
                    // CRITICAL: If server restarted with cancelling task, skip it completely
                    if (task.status === 'cancelling') {
                        console.warn(`[SNAPSHOT-DEBUG] Found cancelling task for ${task.path}. Skipping task restoration.`);
                        continue; // Skip adding this task to the maps
                    }
                    
                    // Ensure processedFileIndex exists (default to processedFiles if available)
                    if (typeof task.processedFileIndex !== 'number') {
                        task.processedFileIndex = task.processedFiles || 0;
                    }
                    
                    this.indexingTasks.set(task.taskId, task);
                    this.pathToTaskIdMap.set(task.path, task.taskId);
                } else {
                    console.warn(`[SNAPSHOT-DEBUG] Task path no longer exists, skipping: ${task.path}`);
                }
            }

            console.log(`[SNAPSHOT-DEBUG] Restored ${this.indexedCodebases.length} indexed codebases`);
            console.log(`[SNAPSHOT-DEBUG] Restored ${this.indexedProjects.length} indexed projects`);
            console.log(`[SNAPSHOT-DEBUG] Restored ${this.indexingTasks.size} active/paused tasks`);

        } catch (error: any) {
            console.error('[SNAPSHOT-DEBUG] Error loading snapshot:', error);
            console.log('[SNAPSHOT-DEBUG] Starting with empty state due to snapshot load error.');
        }
    }

    /**
     * Generate collection name from codebase path
     */
    private generateCollectionName(codebasePath: string): string {
        const normalizedPath = path.resolve(codebasePath);
        const hash = crypto.createHash('md5').update(normalizedPath).digest('hex');
        return `code_chunks_${hash.substring(0, 8)}`;
    }

    /**
     * Save codebase snapshot with throttling to prevent excessive I/O
     */
    private saveCodebaseSnapshot(delay: number = 3000): void {
        // Mark that we have a pending save
        this.pendingSnapshotSave = true;
        
        // If a save is already scheduled, cancel it
        if (this.snapshotSaveTimeout) {
            clearTimeout(this.snapshotSaveTimeout);
        }
        
        // Schedule a new save for the future
        this.snapshotSaveTimeout = setTimeout(async () => {
            // Use a promise to ensure concurrent saves don't overlap
            if (!this.snapshotSavePromise) {
                this.snapshotSavePromise = this._saveCodebaseSnapshotAsync()
                    .catch(error => {
                        console.error('[SNAPSHOT-DEBUG] Error in async snapshot save:', error);
                    })
                    .finally(() => {
                        this.snapshotSavePromise = null;
                        this.pendingSnapshotSave = false;
                    });
                await this.snapshotSavePromise;
            }
            this.snapshotSaveTimeout = null;
        }, delay);
        
        console.log(`[SNAPSHOT-DEBUG] Snapshot save scheduled in ${delay}ms`);
    }

    /**
     * Actually save the codebase snapshot to file
     */
    private async _saveCodebaseSnapshotAsync(): Promise<void> {
        console.log('[SNAPSHOT-DEBUG] Writing snapshot to disk...', this.snapshotFilePath);

        try {
            const snapshotDir = path.dirname(this.snapshotFilePath);
            if (!fs.existsSync(snapshotDir)) {
                fs.mkdirSync(snapshotDir, { recursive: true });
            }

            const snapshot: CodebaseSnapshot = {
                indexedCodebases: this.indexedCodebases, // Keep for backward compatibility
                indexingCodebases: [], // Reset as we manage via indexingTasks
                indexedProjects: this.indexedProjects, // New detailed project information
                // Convert Map to Array for serialization
                activeTasks: Array.from(this.indexingTasks.values()),
                lastUpdated: new Date().toISOString()
            };

            // Atomic write: write to temp file first
            const tempFile = `${this.snapshotFilePath}.tmp`;
            await fs.promises.writeFile(tempFile, JSON.stringify(snapshot, null, 2));
            
            // Then atomically rename to target file
            await fs.promises.rename(tempFile, this.snapshotFilePath);
            
            console.log('[SNAPSHOT-DEBUG] Snapshot saved successfully. Indexed:', this.indexedProjects.length, 'In-progress tasks:', this.indexingTasks.size);

        } catch (error: any) {
            console.error('[SNAPSHOT-DEBUG] Error saving snapshot:', error);
            throw error;
        }
    }

    /**
     * Flush any pending snapshot to disk immediately (for graceful shutdown)
     */
    public async flushSnapshot(): Promise<void> {
        if (this.snapshotSaveTimeout) {
            console.log('[SNAPSHOT-DEBUG] Flushing pending snapshot on shutdown...');
            clearTimeout(this.snapshotSaveTimeout);
            this.snapshotSaveTimeout = null;
        }
        
        // Wait for any in-progress save to complete
        if (this.snapshotSavePromise) {
            await this.snapshotSavePromise;
        }
        
        // Do a final save if there was pending data
        if (this.pendingSnapshotSave) {
            await this._saveCodebaseSnapshotAsync();
        }
    }

    /**
     * Resume background indexing for a paused task
     */
    private async resumeBackgroundIndexing(task: IndexingTask) {
        const absolutePath = task.path;

        // CRITICAL: Acquire indexing lock to prevent conflicts with other processes
        if (!this.syncManager.acquireIndexingLock()) {
            console.warn(`[RESUME-INDEX] Another process is performing indexing/sync operations. Cannot resume ${absolutePath}`);
            // Update task status back to paused since we couldn't acquire lock
            const taskId = this.pathToTaskIdMap.get(absolutePath);
            if (taskId) {
                this.snapshotManager.updateTask(taskId, {
                    status: 'paused',
                    lastError: 'Another MCP instance is performing indexing operations'
                });
                const memoryTask = this.indexingTasks.get(taskId);
                if (memoryTask) {
                    memoryTask.status = 'paused';
                    memoryTask.lastError = 'Another MCP instance is performing indexing operations';
                }
                this.saveCodebaseSnapshot();
            }
            return;
        }

        try {
            console.log(`[RESUME-INDEX] Resuming background indexing for: ${absolutePath}`);
            console.log(`[RESUME-INDEX] Task has ${task.processedFilePaths?.length || 0} already processed files`);

            // CRITICAL: Save previous counts for accumulation
            const previousTotalChunks = task.totalChunks || 0;
            const previousTotalFiles = task.totalFiles || 0;
            console.log(`[RESUME-INDEX] Previous total chunks: ${previousTotalChunks}, Previous total files: ${previousTotalFiles}`);

            // Use the existing CodeContext instance for indexing.
            let contextForThisTask = this.codeContext;

            // Generate collection name
            const finalCollectionName = this.generateCollectionName(absolutePath);

            // CRITICAL: Check if synchronizer already exists to preserve Merkle DAG state
            let synchronizer = this.codeContext['synchronizers'].get(finalCollectionName);
            
            if (!synchronizer) {
                // Initialize file synchronizer with proper ignore patterns only if it doesn't exist
                const { FileSynchronizer } = await import("@zilliz/code-context-core");
                const ignorePatterns = this.codeContext['ignorePatterns'] || [];
                console.log(`[RESUME-INDEX] Creating new synchronizer with ignore patterns: ${ignorePatterns.join(', ')}`);
                synchronizer = new FileSynchronizer(absolutePath, ignorePatterns);
                await synchronizer.initialize();

                // Store synchronizer in the context's internal map
                this.codeContext['synchronizers'].set(finalCollectionName, synchronizer);
                if (contextForThisTask !== this.codeContext) {
                    contextForThisTask['synchronizers'].set(finalCollectionName, synchronizer);
                }
            } else {
                console.log(`[RESUME-INDEX] ✅ Reusing existing synchronizer (preserves Merkle DAG state)`);
            }

            console.log(`[RESUME-INDEX] Starting indexing with ${task.splitter} splitter for: ${absolutePath}`);

            // Log embedding provider information before indexing
            const embeddingProvider = this.codeContext['embedding'];
            console.log(`[RESUME-INDEX] 🧠 Using embedding provider: ${embeddingProvider.getProvider()} with dimension: ${embeddingProvider.getDimension()}`);

            // Start indexing with the appropriate context and progress callback
            console.log(`[RESUME-INDEX] 🚀 Beginning codebase indexing process...`);
            
            // Create progress callback to update task status
            const progressCallback = (progress: { phase: string; current: number; total: number; percentage: number }) => {
                const taskId = this.pathToTaskIdMap.get(absolutePath);
                if (taskId) {
                    // CRITICAL: Update SnapshotManager first (this is what API endpoints read from)
                    this.snapshotManager.updateTask(taskId, {
                        totalFiles: progress.total,
                        processedFiles: progress.current,
                        processedFileIndex: progress.current,
                        phase: progress.phase || 'Processing files',
                        progress: progress.total > 0 ? (progress.current / progress.total) * 100 : 0
                    });
                    
                    // CRITICAL: Also update indexingTasks for memory consistency 
                    const currentTask = this.indexingTasks.get(taskId);
                    if (currentTask) {
                        // Update progress information based on phase
                        if (progress.phase.includes('files') || progress.phase.includes('文件')) {
                            currentTask.totalFiles = progress.total;
                            currentTask.processedFiles = progress.current;
                        }
                        // Update processed file index for other phases
                        currentTask.processedFileIndex = progress.current;
                        currentTask.phase = progress.phase || 'Processing files';
                        currentTask.progress = progress.total > 0 ? (progress.current / progress.total) * 100 : 0;
                    }
                    
                    // Save snapshot periodically (every 10 files)
                    if (progress.current % 10 === 0) {
                        this.snapshotManager.saveCodebaseSnapshot();
                    }
                    
                    console.log(`[RESUME-PROGRESS] ${progress.phase}: ${progress.current}/${progress.total} (${progress.percentage}%)`);
                }
            };

            // Create pause/cancel check callback that will be called at key points during indexing
            const pauseCheck = async (): Promise<void> => {
                const taskId = this.pathToTaskIdMap.get(absolutePath);
                if (taskId) {
                    const currentTask = this.indexingTasks.get(taskId);
                    if (currentTask) {
                        if (currentTask.status === 'paused') {
                            console.log(`[RESUME-INDEX] ⏸️  Task ${taskId} is paused, stopping indexing process...`);
                            throw new Error('TASK_PAUSED');
                        }
                        if (currentTask.status === 'cancelling') {
                            console.log(`[RESUME-INDEX] 🗑️ Task ${taskId} is cancelling, stopping indexing process...`);
                            throw new Error('TASK_CANCELLED');
                        }
                    }
                }
            };

            // Create file processed callback for resume functionality
            const onFileProcessed = (filePath: string): void => {
                const taskId = this.pathToTaskIdMap.get(absolutePath);
                if (taskId) {
                    // CRITICAL: Get task from SnapshotManager first (authoritative source)
                    const currentTask = this.snapshotManager.getTask(taskId);
                    if (currentTask) {
                        // Initialize processedFilePaths if not exists
                        if (!currentTask.processedFilePaths) {
                            currentTask.processedFilePaths = [];
                        }
                        // Add processed file to the list
                        if (!currentTask.processedFilePaths.includes(filePath)) {
                            currentTask.processedFilePaths.push(filePath);
                            
                            // CRITICAL: Update the task in SnapshotManager properly
                            // Calculate total chunks as previous chunks + estimated new chunks
                            const newFilesProcessed = currentTask.processedFilePaths.length;
                            const estimatedNewChunks = Math.floor(newFilesProcessed * 7); // ~7 chunks per file estimate
                            const estimatedTotalChunks = previousTotalChunks + estimatedNewChunks;
                            
                            this.snapshotManager.updateTask(taskId, {
                                processedFilePaths: [...currentTask.processedFilePaths], // Create new array
                                processedFileIndex: currentTask.processedFilePaths.length,
                                // CRITICAL: Use accumulated estimate (previous + new)
                                totalChunks: estimatedTotalChunks
                            });
                            
                            // Also update memory task for API compatibility
                            const memoryTask = this.indexingTasks.get(taskId);
                            if (memoryTask) {
                                if (!memoryTask.processedFilePaths) {
                                    memoryTask.processedFilePaths = [];
                                }
                                memoryTask.processedFilePaths.push(filePath);
                                memoryTask.processedFileIndex = memoryTask.processedFilePaths.length;
                                memoryTask.totalChunks = estimatedTotalChunks; // CRITICAL: Use accumulated estimate
                            }
                            
                            // Save snapshot with a longer delay (10 seconds) to avoid too frequent writes
                            this.saveCodebaseSnapshot(10000);
                        }
                    }
                }
            };

            // Use saved processed file paths from the task
            const processedFilePaths = task.processedFilePaths || [];

            if (processedFilePaths.length > 0) {
                console.log(`[RESUME-INDEX] 🔄 Resuming from ${processedFilePaths.length} already processed files`);
            }

            const stats = await contextForThisTask.indexCodebase(absolutePath, {
                progressCallback,
                pauseCheck,
                onFileProcessed,
                processedFilePaths
            });
            // CRITICAL: Calculate accumulated totals (previous + new)
            const accumulatedTotalChunks = previousTotalChunks + stats.totalChunks;
            const accumulatedTotalFiles = previousTotalFiles + stats.indexedFiles;
            console.log(`[RESUME-INDEX] ✅ Indexing completed successfully! New Files: ${stats.indexedFiles}, Total Files: ${accumulatedTotalFiles}, New Chunks: ${stats.totalChunks}, Total Chunks: ${accumulatedTotalChunks}`);

            // Move from indexing to indexed list - update task status with final stats
            if (taskId) {
                // CRITICAL: Update SnapshotManager first (authoritative source for API)
                const completedTask = this.snapshotManager.getTask(taskId);
                if (completedTask) {
                    this.snapshotManager.updateTask(taskId, {
                        status: 'completed',
                        endTime: Date.now(),
                        totalChunks: accumulatedTotalChunks,  // CRITICAL: Use accumulated total!
                        processedFiles: accumulatedTotalFiles,  // CRITICAL: Use accumulated total!
                        totalFiles: accumulatedTotalFiles,  // CRITICAL: Use accumulated total!
                        progress: 100,
                        phase: 'Completed'
                    });
                    
                    // CRITICAL: Also update indexedProjects record with final stats
                    this.snapshotManager.updateIndexedProject(absolutePath, {
                        totalFiles: accumulatedTotalFiles,  // CRITICAL: Use accumulated total!
                        totalChunks: accumulatedTotalChunks,  // CRITICAL: Use accumulated total!
                        status: 'completed',
                        indexedAt: new Date().toISOString()
                    });
                    
                    // Also update memory task for consistency
                    const memoryTask = this.indexingTasks.get(taskId);
                    if (memoryTask) {
                        memoryTask.status = 'completed';
                        memoryTask.endTime = Date.now();
                        memoryTask.totalChunks = accumulatedTotalChunks;  // CRITICAL: Use accumulated total!
                        memoryTask.processedFiles = accumulatedTotalFiles;  // CRITICAL: Use accumulated total!
                        memoryTask.totalFiles = accumulatedTotalFiles;  // CRITICAL: Use accumulated total!
                        memoryTask.progress = 100;
                        memoryTask.phase = 'Completed';
                    }
                }
                
                // Move from indexing to indexed list  
                this.snapshotManager.moveFromIndexingToIndexed(absolutePath);
                
                // Remove completed task from active tasks (after updating SnapshotManager)
                this.indexingTasks.delete(taskId);
                this.pathToTaskIdMap.delete(absolutePath);
            }
            
            // Add to backward compatibility list
            if (!this.indexedCodebases.includes(absolutePath)) {
                this.indexedCodebases.push(absolutePath);
            }

            // Add to detailed project information
            const existingProjectIndex = this.indexedProjects.findIndex(p => p.path === absolutePath);
            const projectInfo: IndexedProject = {
                path: absolutePath,
                indexedAt: new Date().toISOString(),
                totalFiles: accumulatedTotalFiles,  // CRITICAL: Use accumulated total!
                totalChunks: accumulatedTotalChunks,  // CRITICAL: Use accumulated total!
                splitter: task.splitter || 'ast',
                collectionName: this.generateCollectionName(absolutePath),
                status: 'completed'
            };

            if (existingProjectIndex >= 0) {
                // Update existing project
                this.indexedProjects[existingProjectIndex] = projectInfo;
            } else {
                // Add new project
                this.indexedProjects.push(projectInfo);
            }

            this.indexingStats = { indexedFiles: accumulatedTotalFiles, totalChunks: accumulatedTotalChunks };

            // Save snapshot after updating codebase lists
            this.saveCodebaseSnapshot();

            let message = `Resume indexing completed for '${absolutePath}' using ${(task.splitter || 'AST').toUpperCase()} splitter.\nIndexed ${accumulatedTotalFiles} total files (${stats.indexedFiles} new + ${previousTotalFiles} previous), ${accumulatedTotalChunks} total chunks (${stats.totalChunks} new + ${previousTotalChunks} previous).`;
            if (stats.status === 'limit_reached') {
                message += `\n⚠️  Warning: Indexing stopped because the chunk limit (450,000) was reached. The index may be incomplete.`;
            }

            console.log(`[RESUME-INDEX] ${message}`);
            
            // CRITICAL: Release indexing lock after successful completion
            this.syncManager.releaseIndexingLock();

        } catch (error: any) {
            const taskId = this.pathToTaskIdMap.get(absolutePath);
            
            // Handle task cancellation
            if (error.message === 'TASK_CANCELLED') {
                console.log(`[RESUME-INDEX] 🗑️ Indexing cancelled for ${absolutePath}. Cleaning up...`);
                try {
                    // CRITICAL: Clear any partially created index to prevent orphans
                    await this.codeContext.clearIndex(absolutePath);
                    console.log(`[RESUME-INDEX] ✅ Partially created index for ${absolutePath} cleared.`);
                } catch (cleanupError: any) {
                    console.error(`[RESUME-INDEX] ❌ Failed to clean up index for cancelled task ${absolutePath}:`, cleanupError);
                } finally {
                    // CRITICAL: Release indexing lock when task is cancelled during indexing
                    this.syncManager.releaseIndexingLock();
                    
                    // Remove the task completely
                    if (taskId) {
                        this.indexingTasks.delete(taskId);
                        this.pathToTaskIdMap.delete(absolutePath);
                    }
                    // Also remove from the main projects list
                    this.indexedProjects = this.indexedProjects.filter(p => p.path !== absolutePath);
                    this.saveCodebaseSnapshot();
                }
                return;
            }
            
            // Handle task pause differently from actual errors
            if (error.message === 'TASK_PAUSED') {
                console.log(`[RESUME-INDEX] ⏸️  Indexing paused for ${absolutePath}`);
                // Task is already marked as paused by the progress callback
                // No need to change status or save snapshot
                
                // CRITICAL: Release indexing lock when task is paused
                this.syncManager.releaseIndexingLock();
                return;
            }
            
            console.error(`[RESUME-INDEX] Error during resume indexing for ${absolutePath}:`, error);
            // Update task status to error on indexing failure
            if (taskId) {
                const currentTask = this.indexingTasks.get(taskId);
                if (currentTask) {
                    currentTask.status = 'error';
                    currentTask.lastError = error.message || error.toString();
                }
            }

            // Update project status to error
            const projectRecord = this.indexedProjects.find(p => p.path === absolutePath);
            if (projectRecord) {
                projectRecord.status = 'error';  
                projectRecord.errorMessage = error.message || error.toString();
            }

            this.saveCodebaseSnapshot();

            // Log error but don't crash MCP service - indexing errors are handled gracefully
            console.error(`[RESUME-INDEX] Resume indexing failed for ${absolutePath}: ${error.message || error}`);
            
            // CRITICAL: Release indexing lock after error handling
            this.syncManager.releaseIndexingLock();
        }
    }

}

// Main execution
async function main() {
    // Parse command line arguments
    const args = process.argv.slice(2);

    // Show help if requested
    if (args.includes('--help') || args.includes('-h')) {
        showHelpMessage();
        process.exit(0);
    }

    // Create configuration
    const config = createMcpConfig();
    logConfigurationSummary(config);

    const server = new CodeContextMcpServer(config);
    await server.start();
}

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.error("Received SIGINT, shutting down gracefully...");
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.error("Received SIGTERM, shutting down gracefully...");
    process.exit(0);
});

// Always start the server - this is designed to be the main entry point
main().catch((error) => {
    console.error("Fatal error:", error);
    process.exit(1);
});