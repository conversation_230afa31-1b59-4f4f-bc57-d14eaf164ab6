import * as fs from "fs";
import * as path from "path";
import * as os from "os";
import { CodeContext } from "@zilliz/code-context-core";
import { SnapshotManager } from "./snapshot.js";
import { IndexingTask } from "./config.js";

export class SyncManager {
    private codeContext: CodeContext;
    private snapshotManager: SnapshotManager;
    private indexingTasks: Map<string, IndexingTask>;
    private isSyncing: boolean = false;
    private globalSyncLockFile: string;
    private activeIndexingCount: number = 0; // Track concurrent indexing tasks

    constructor(codeContext: CodeContext, snapshotManager: SnapshotManager, indexingTasks: Map<string, IndexingTask>) {
        this.codeContext = codeContext;
        this.snapshotManager = snapshotManager;
        this.indexingTasks = indexingTasks;
        this.globalSyncLockFile = path.join(os.homedir(), '.codecontext', 'sync.lock');
    }

    public async handleSyncIndex(): Promise<void> {
        const syncStartTime = Date.now();
        console.log(`[SYNC-DEBUG] handleSyncIndex() called at ${new Date().toISOString()}`);

        // Check for active indexing tasks first - this prevents sync conflicts
        if (this.hasActiveIndexingTasks()) {
            console.log('[SYNC-DEBUG] Active indexing tasks detected. Skipping periodic sync to avoid conflicts.');
            return;
        }

        // Check for global sync lock - prevents multiple MCP instances from syncing simultaneously
        if (!this.acquireGlobalSyncLock()) {
            console.log('[SYNC-DEBUG] Another MCP instance is performing sync. Skipping this sync cycle.');
            return;
        }

        const indexedCodebases = this.snapshotManager.getIndexedCodebases();

        if (indexedCodebases.length === 0) {
            console.log('[SYNC-DEBUG] No codebases indexed. Skipping sync.');
            this.releaseGlobalSyncLock();
            return;
        }

        console.log(`[SYNC-DEBUG] Found ${indexedCodebases.length} indexed codebases:`, indexedCodebases);

        if (this.isSyncing) {
            console.log('[SYNC-DEBUG] Index sync already in progress. Skipping.');
            this.releaseGlobalSyncLock();
            return;
        }

        this.isSyncing = true;
        console.log(`[SYNC-DEBUG] Starting index sync for all ${indexedCodebases.length} codebases...`);

        try {
            let totalStats = { added: 0, removed: 0, modified: 0 };

            for (let i = 0; i < indexedCodebases.length; i++) {
                const codebasePath = indexedCodebases[i];
                const codebaseStartTime = Date.now();

                console.log(`[SYNC-DEBUG] [${i + 1}/${indexedCodebases.length}] Starting sync for codebase: '${codebasePath}'`);

                // Check if codebase path still exists
                try {
                    const pathExists = fs.existsSync(codebasePath);
                    console.log(`[SYNC-DEBUG] Codebase path exists: ${pathExists}`);

                    if (!pathExists) {
                        console.warn(`[SYNC-DEBUG] Codebase path '${codebasePath}' no longer exists. Skipping sync.`);
                        continue;
                    }
                } catch (pathError: any) {
                    console.error(`[SYNC-DEBUG] Error checking codebase path '${codebasePath}':`, pathError);
                    continue;
                }

                try {
                    console.log(`[SYNC-DEBUG] Calling codeContext.reindexByChange() for '${codebasePath}'`);
                    const stats = await this.codeContext.reindexByChange(codebasePath);
                    const codebaseElapsed = Date.now() - codebaseStartTime;

                    console.log(`[SYNC-DEBUG] Reindex stats for '${codebasePath}':`, stats);
                    console.log(`[SYNC-DEBUG] Codebase sync completed in ${codebaseElapsed}ms`);

                    // Accumulate total stats
                    totalStats.added += stats.added;
                    totalStats.removed += stats.removed;
                    totalStats.modified += stats.modified;

                    if (stats.added > 0 || stats.removed > 0 || stats.modified > 0) {
                        console.log(`[SYNC] Sync complete for '${codebasePath}'. Added: ${stats.added}, Removed: ${stats.removed}, Modified: ${stats.modified} (${codebaseElapsed}ms)`);
                    } else {
                        console.log(`[SYNC] No changes detected for '${codebasePath}' (${codebaseElapsed}ms)`);
                    }
                } catch (error: any) {
                    const codebaseElapsed = Date.now() - codebaseStartTime;
                    console.error(`[SYNC-DEBUG] Error syncing codebase '${codebasePath}' after ${codebaseElapsed}ms:`, error);
                    console.error(`[SYNC-DEBUG] Error stack:`, error.stack);

                    // Log additional error details
                    if (error.code) {
                        console.error(`[SYNC-DEBUG] Error code: ${error.code}`);
                    }
                    if (error.errno) {
                        console.error(`[SYNC-DEBUG] Error errno: ${error.errno}`);
                    }

                    // Continue with next codebase even if one fails
                }
            }

            const totalElapsed = Date.now() - syncStartTime;
            console.log(`[SYNC-DEBUG] Total sync stats across all codebases: Added: ${totalStats.added}, Removed: ${totalStats.removed}, Modified: ${totalStats.modified}`);
            console.log(`[SYNC-DEBUG] Index sync completed for all codebases in ${totalElapsed}ms`);
            console.log(`[SYNC] Index sync completed for all codebases. Total changes - Added: ${totalStats.added}, Removed: ${totalStats.removed}, Modified: ${totalStats.modified}`);
        } catch (error: any) {
            const totalElapsed = Date.now() - syncStartTime;
            console.error(`[SYNC-DEBUG] Error during index sync after ${totalElapsed}ms:`, error);
            console.error(`[SYNC-DEBUG] Error stack:`, error.stack);
        } finally {
            this.isSyncing = false;
            this.releaseGlobalSyncLock();
            const totalElapsed = Date.now() - syncStartTime;
            console.log(`[SYNC-DEBUG] handleSyncIndex() finished at ${new Date().toISOString()}, total duration: ${totalElapsed}ms`);
        }
    }

    public startBackgroundSync(): void {
        console.log('[SYNC-DEBUG] startBackgroundSync() called');

        // Execute initial sync immediately after a short delay to let server initialize
        console.log('[SYNC-DEBUG] Scheduling initial sync in 5 seconds...');
        setTimeout(async () => {
            console.log('[SYNC-DEBUG] Executing initial sync after server startup');
            try {
                await this.handleSyncIndex();
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                if (errorMessage.includes('Failed to query collection')) {
                    console.log('[SYNC-DEBUG] Collection not yet established, this is expected for new cluster users. Will retry on next sync cycle.');
                } else {
                    console.error('[SYNC-DEBUG] Initial sync failed with unexpected error:', error);
                    throw error;
                }
            }
        }, 5000); // Initial sync after 5 seconds

        // Periodically check for file changes and update the index
        console.log('[SYNC-DEBUG] Setting up periodic sync every 5 minutes (300000ms)');
        const syncInterval = setInterval(() => {
            console.log('[SYNC-DEBUG] Executing scheduled periodic sync');
            this.handleSyncIndex();
        }, 5 * 60 * 1000); // every 5 minutes

        console.log('[SYNC-DEBUG] Background sync setup complete. Interval ID:', syncInterval);
    }

    /**
     * Check if there are any active indexing tasks
     * @returns true if there are tasks with 'indexing' status
     */
    private hasActiveIndexingTasks(): boolean {
        const activeTasks = Array.from(this.indexingTasks.values())
            .filter(task => task.status === 'indexing');
        
        if (activeTasks.length > 0) {
            console.log(`[SYNC-DEBUG] Found ${activeTasks.length} active indexing task(s):`, 
                activeTasks.map(task => ({ taskId: task.taskId, path: task.path, status: task.status })));
            return true;
        }
        
        return false;
    }

    /**
     * Acquire global sync lock to prevent multiple MCP instances from syncing simultaneously
     * @returns true if lock acquired successfully, false if another instance is syncing
     */
    private acquireGlobalSyncLock(): boolean {
        try {
            // Ensure lock directory exists
            const lockDir = path.dirname(this.globalSyncLockFile);
            if (!fs.existsSync(lockDir)) {
                fs.mkdirSync(lockDir, { recursive: true });
            }

            // Check if lock file exists and is recent (within 10 minutes)
            if (fs.existsSync(this.globalSyncLockFile)) {
                const lockStat = fs.statSync(this.globalSyncLockFile);
                const lockAge = Date.now() - lockStat.mtime.getTime();
                const lockTimeoutMs = 10 * 60 * 1000; // 10 minutes

                if (lockAge < lockTimeoutMs) {
                    // Lock is still valid, check if it's from the same process
                    try {
                        const lockContent = fs.readFileSync(this.globalSyncLockFile, 'utf8');
                        const lockData = JSON.parse(lockContent);
                        
                        if (lockData.pid === process.pid) {
                            // Same process - allow concurrent indexing tasks within same MCP instance
                            console.log(`[SYNC-DEBUG] Lock held by same process (PID: ${process.pid}). Allowing concurrent indexing.`);
                            return true;
                        } else {
                            // Different process - block to prevent conflicts between MCP instances
                            console.log(`[SYNC-DEBUG] Global sync lock held by different process (PID: ${lockData.pid}, age: ${Math.round(lockAge/1000)}s). Blocking concurrent MCP instance.`);
                            return false;
                        }
                    } catch (parseError) {
                        // If we can't parse the lock file, assume it's from another process and block
                        console.warn(`[SYNC-DEBUG] Could not parse lock file, blocking access:`, parseError);
                        return false;
                    }
                } else {
                    // Stale lock, remove it
                    console.log(`[SYNC-DEBUG] Removing stale sync lock (age: ${Math.round(lockAge/1000)}s)`);
                    fs.unlinkSync(this.globalSyncLockFile);
                }
            }

            // Create lock file with current timestamp
            const lockData = {
                pid: process.pid,
                timestamp: new Date().toISOString(),
                hostname: (typeof os.hostname === 'function' ? os.hostname() : 'unknown')
            };
            fs.writeFileSync(this.globalSyncLockFile, JSON.stringify(lockData, null, 2));
            console.log(`[SYNC-DEBUG] Acquired global sync lock (PID: ${process.pid})`);
            return true;

        } catch (error) {
            console.warn(`[SYNC-DEBUG] Failed to acquire global sync lock:`, error);
            // If we can't acquire lock due to permission or other issues, proceed anyway
            return true;
        }
    }

    /**
     * Release global sync lock
     */
    private releaseGlobalSyncLock(): void {
        try {
            if (fs.existsSync(this.globalSyncLockFile)) {
                fs.unlinkSync(this.globalSyncLockFile);
                console.log(`[SYNC-DEBUG] Released global sync lock`);
            }
        } catch (error) {
            console.warn(`[SYNC-DEBUG] Failed to release global sync lock:`, error);
        }
    }

    /**
     * Public method to forcefully clear sync lock when tasks are manually stopped
     * Called when user pauses, cancels, or deletes indexing tasks
     * This resets the reference count and forces lock file deletion
     */
    public clearSyncLock(): void {
        try {
            if (fs.existsSync(this.globalSyncLockFile)) {
                fs.unlinkSync(this.globalSyncLockFile);
                console.log(`[SYNC-DEBUG] Cleared sync lock due to manual task interruption (had ${this.activeIndexingCount} active tasks)`);
            }
            // Reset the active indexing count since all tasks are being manually stopped
            this.activeIndexingCount = 0;
        } catch (error) {
            console.warn(`[SYNC-DEBUG] Failed to clear sync lock:`, error);
        }
    }

    /**
     * Public method to acquire indexing lock for index/resume operations
     * This prevents multiple processes from interfering with indexing
     */
    public acquireIndexingLock(): boolean {
        const acquired = this.acquireGlobalSyncLock();
        if (acquired) {
            this.activeIndexingCount++;
            console.log(`[SYNC-DEBUG] Indexing lock acquired. Active indexing tasks: ${this.activeIndexingCount}`);
        }
        return acquired;
    }

    /**
     * Public method to release indexing lock after index/resume operations
     */
    public releaseIndexingLock(): void {
        if (this.activeIndexingCount > 0) {
            this.activeIndexingCount--;
            console.log(`[SYNC-DEBUG] Indexing lock released. Active indexing tasks: ${this.activeIndexingCount}`);
            
            // Only release the actual lock file when no more active indexing tasks
            if (this.activeIndexingCount === 0) {
                this.releaseGlobalSyncLock();
            }
        } else {
            console.warn(`[SYNC-DEBUG] Attempted to release indexing lock but no active tasks tracked`);
        }
    }
} 