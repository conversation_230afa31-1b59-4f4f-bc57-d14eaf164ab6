import * as fs from "fs";
import * as path from "path";
import * as crypto from "crypto";
import { CodeContext, COLLECTION_LIMIT_MESSAGE, DEFAULT_IGNORE_PATTERNS, DEFAULT_SUPPORTED_EXTENSIONS } from "@zilliz/code-context-core";
import { SnapshotManager } from "./snapshot.js";
import { SyncManager } from "./sync.js";
import { ensureAbsolutePath, truncateContent, trackCodebasePath } from "./utils.js";
import { IndexingTask } from "./config.js";

export class ToolHandlers {
    private codeContext: CodeContext;
    private snapshotManager: SnapshotManager;
    private syncManager: SyncManager;
    private indexingStats: { indexedFiles: number; totalChunks: number } | null = null;
    private currentWorkspace: string;
    
    // Task lookup optimization
    private pathToTaskIdMap: Map<string, string> = new Map();
    
    // Reference to the main indexing tasks map for sync
    private indexingTasks: Map<string, IndexingTask>;

    constructor(codeContext: CodeContext, snapshotManager: SnapshotManager, syncManager: SyncManager, indexingTasks: Map<string, IndexingTask>) {
        this.codeContext = codeContext;
        this.snapshotManager = snapshotManager;
        this.syncManager = syncManager;
        this.indexingTasks = indexingTasks;
        this.currentWorkspace = process.cwd();
        console.log(`[WORKSPACE] Current workspace: ${this.currentWorkspace}`);
        
        // Build task lookup map from loaded data
        this.buildTaskLookupMap();
    }
    
    /**
     * Build task lookup map from loaded data
     */
    private buildTaskLookupMap(): void {
        this.pathToTaskIdMap.clear();
        for (const task of this.snapshotManager.getActiveTasks()) {
            this.pathToTaskIdMap.set(task.path, task.taskId);
        }
        console.log(`[TASK-MAP] Built lookup map with ${this.pathToTaskIdMap.size} entries`);
    }
    
    /**
     * Generate a unique task ID for a codebase path
     * @param codebasePath Absolute path to the codebase
     * @returns 8-character MD5 hash for stable identification
     */
    private generateTaskId(codebasePath: string): string {
        const normalizedPath = path.resolve(codebasePath).replace(/\\/g, '/');
        const hash = crypto.createHash('md5').update(normalizedPath).digest('hex');
        return hash.substring(0, 8);
    }

    /**
     * Sync indexed codebases from Zilliz Cloud collections
     * This method fetches all collections from the vector database,
     * gets the first document from each collection to extract codebasePath from metadata,
     * and updates the snapshot with discovered codebases.
     * 
     * Logic: Compare mcp-codebase-snapshot.json with zilliz cloud collections
     * - If local snapshot has extra directories (not in cloud), remove them
     * - If local snapshot is missing directories (exist in cloud), ignore them
     */
    private async syncIndexedCodebasesFromCloud(): Promise<void> {
        try {
            console.log(`[SYNC-CLOUD] 🔄 Syncing indexed codebases from Zilliz Cloud...`);

            // Get all collections using the interface method
            const vectorDb = this.codeContext['vectorDatabase'];

            // Use the new listCollections method from the interface
            const collections = await vectorDb.listCollections();

            console.log(`[SYNC-CLOUD] 📋 Found ${collections.length} collections in Zilliz Cloud`);

            if (collections.length === 0) {
                console.log(`[SYNC-CLOUD] ✅ No collections found in cloud`);
                // If no collections in cloud, remove all local codebases
                const localCodebases = this.snapshotManager.getIndexedCodebases();
                if (localCodebases.length > 0) {
                    console.log(`[SYNC-CLOUD] 🧹 Removing ${localCodebases.length} local codebases as cloud has no collections`);
                    for (const codebasePath of localCodebases) {
                        this.snapshotManager.removeIndexedCodebase(codebasePath);
                        console.log(`[SYNC-CLOUD] ➖ Removed local codebase: ${codebasePath}`);
                    }
                    this.snapshotManager.saveCodebaseSnapshot();
                    console.log(`[SYNC-CLOUD] 💾 Updated snapshot to match empty cloud state`);
                }
                return;
            }

            const cloudCodebases = new Set<string>();

            // Check each collection for codebase path
            for (const collectionName of collections) {
                try {
                    // Skip collections that don't match the code_chunks pattern
                    if (!collectionName.startsWith('code_chunks_')) {
                        console.log(`[SYNC-CLOUD] ⏭️  Skipping non-code collection: ${collectionName}`);
                        continue;
                    }

                    console.log(`[SYNC-CLOUD] 🔍 Checking collection: ${collectionName}`);

                    // Query the first document to get metadata
                    const results = await vectorDb.query(
                        collectionName,
                        '', // Empty filter to get all results
                        ['metadata'], // Only fetch metadata field
                        1 // Only need one result to extract codebasePath
                    );

                    if (results && results.length > 0) {
                        const firstResult = results[0];
                        const metadataStr = firstResult.metadata;

                        if (metadataStr) {
                            try {
                                const metadata = JSON.parse(metadataStr);
                                const codebasePath = metadata.codebasePath;

                                if (codebasePath && typeof codebasePath === 'string') {
                                    console.log(`[SYNC-CLOUD] 📍 Found codebase path: ${codebasePath} in collection: ${collectionName}`);
                                    cloudCodebases.add(codebasePath);
                                } else {
                                    console.warn(`[SYNC-CLOUD] ⚠️  No codebasePath found in metadata for collection: ${collectionName}`);
                                }
                            } catch (parseError) {
                                console.warn(`[SYNC-CLOUD] ⚠️  Failed to parse metadata JSON for collection ${collectionName}:`, parseError);
                            }
                        } else {
                            console.warn(`[SYNC-CLOUD] ⚠️  No metadata found in collection: ${collectionName}`);
                        }
                    } else {
                        console.log(`[SYNC-CLOUD] ℹ️  Collection ${collectionName} is empty`);
                    }
                } catch (collectionError: any) {
                    console.warn(`[SYNC-CLOUD] ⚠️  Error checking collection ${collectionName}:`, collectionError.message || collectionError);
                    // Continue with next collection
                }
            }

            console.log(`[SYNC-CLOUD] 📊 Found ${cloudCodebases.size} valid codebases in cloud`);

            // Get current local codebases
            const localCodebases = new Set(this.snapshotManager.getIndexedCodebases());
            console.log(`[SYNC-CLOUD] 📊 Found ${localCodebases.size} local codebases in snapshot`);

            let hasChanges = false;

            // Remove local codebases that don't exist in cloud
            for (const localCodebase of localCodebases) {
                if (!cloudCodebases.has(localCodebase)) {
                    this.snapshotManager.removeIndexedCodebase(localCodebase);
                    hasChanges = true;
                    console.log(`[SYNC-CLOUD] ➖ Removed local codebase (not in cloud): ${localCodebase}`);
                }
            }

            // Note: We don't add cloud codebases that are missing locally (as per user requirement)
            console.log(`[SYNC-CLOUD] ℹ️  Skipping addition of cloud codebases not present locally (per sync policy)`);

            if (hasChanges) {
                this.snapshotManager.saveCodebaseSnapshot();
                console.log(`[SYNC-CLOUD] 💾 Updated snapshot to match cloud state`);
            } else {
                console.log(`[SYNC-CLOUD] ✅ Local snapshot already matches cloud state`);
            }

            console.log(`[SYNC-CLOUD] ✅ Cloud sync completed successfully`);
        } catch (error: any) {
            console.error(`[SYNC-CLOUD] ❌ Error syncing codebases from cloud:`, error.message || error);
            // Don't throw - this is not critical for the main functionality
        }
    }

    public async handleIndexCodebase(args: any) {
        const { path: codebasePath, force, splitter, customExtensions, ignorePatterns } = args;
        const forceReindex = force || false;
        const splitterType = splitter || 'ast'; // Default to AST
        const customFileExtensions = customExtensions || [];
        const customIgnorePatterns = ignorePatterns || [];

        try {
            // Sync indexed codebases from cloud first
            await this.syncIndexedCodebasesFromCloud();

            // Validate splitter parameter
            if (splitterType !== 'ast' && splitterType !== 'langchain') {
                return {
                    content: [{
                        type: "text",
                        text: `Error: Invalid splitter type '${splitterType}'. Must be 'ast' or 'langchain'.`
                    }],
                    isError: true
                };
            }
            // Force absolute path resolution - warn if relative path provided
            const absolutePath = ensureAbsolutePath(codebasePath);

            // Validate path exists
            if (!fs.existsSync(absolutePath)) {
                return {
                    content: [{
                        type: "text",
                        text: `Error: Path '${absolutePath}' does not exist. Original input: '${codebasePath}'`
                    }],
                    isError: true
                };
            }

            // Check if it's a directory
            const stat = fs.statSync(absolutePath);
            if (!stat.isDirectory()) {
                return {
                    content: [{
                        type: "text",
                        text: `Error: Path '${absolutePath}' is not a directory`
                    }],
                    isError: true
                };
            }

            // Check if already indexing
            if (this.snapshotManager.getIndexingCodebases().includes(absolutePath)) {
                return {
                    content: [{
                        type: "text",
                        text: `Codebase '${absolutePath}' is already being indexed in the background. Please wait for completion.`
                    }],
                    isError: true
                };
            }

            // Check if already indexed (unless force is true)
            if (!forceReindex && this.snapshotManager.getIndexedCodebases().includes(absolutePath)) {
                return {
                    content: [{
                        type: "text",
                        text: `Codebase '${absolutePath}' is already indexed. Use force=true to re-index.`
                    }],
                    isError: true
                };
            }

            // If force reindex and codebase is already indexed, remove it from indexed list
            if (forceReindex && this.snapshotManager.getIndexedCodebases().includes(absolutePath)) {
                console.log(`[FORCE-REINDEX] 🔄 Removing '${absolutePath}' from indexed list for re-indexing`);
                this.snapshotManager.removeIndexedCodebase(absolutePath);
            }

            // CRITICAL: Pre-index collection creation validation
            try {
                const normalizedPath = path.resolve(absolutePath);
                const hash = crypto.createHash('md5').update(normalizedPath).digest('hex');
                const collectionName = `code_chunks_${hash.substring(0, 8)}`;

                console.log(`[INDEX-VALIDATION] 🔍 Validating collection creation for: ${collectionName}`);

                // Get embedding dimension for collection creation
                const embeddingProvider = this.codeContext['embedding'];
                const dimension = embeddingProvider.getDimension();

                // If force reindex, clear existing collection first
                if (forceReindex) {
                    console.log(`[INDEX-VALIDATION] 🧹 Force reindex enabled, clearing existing collection: ${collectionName}`);
                    try {
                        await this.codeContext['vectorDatabase'].dropCollection(collectionName);
                        console.log(`[INDEX-VALIDATION] ✅ Existing collection cleared: ${collectionName}`);
                    } catch (dropError: any) {
                        // Collection might not exist, which is fine
                        console.log(`[INDEX-VALIDATION] ℹ️  Collection ${collectionName} does not exist or already cleared`);
                    }
                }

                // Attempt to create collection - this will throw COLLECTION_LIMIT_MESSAGE if limit reached
                await this.codeContext['vectorDatabase'].createCollection(
                    collectionName,
                    dimension,
                    `Code context collection: ${collectionName}`
                );

                // If creation succeeds, immediately drop the test collection
                await this.codeContext['vectorDatabase'].dropCollection(collectionName);
                console.log(`[INDEX-VALIDATION] ✅ Collection creation validated successfully`);

            } catch (validationError: any) {
                const errorMessage = typeof validationError === 'string' ? validationError :
                    (validationError instanceof Error ? validationError.message : String(validationError));

                if (errorMessage === COLLECTION_LIMIT_MESSAGE || errorMessage.includes(COLLECTION_LIMIT_MESSAGE)) {
                    console.error(`[INDEX-VALIDATION] ❌ Collection limit validation failed: ${absolutePath}`);

                    // CRITICAL: Immediately return the COLLECTION_LIMIT_MESSAGE to MCP client
                    return {
                        content: [{
                            type: "text",
                            text: COLLECTION_LIMIT_MESSAGE
                        }],
                        isError: true
                    };
                } else {
                    // Handle other collection creation errors
                    console.error(`[INDEX-VALIDATION] ❌ Collection creation validation failed:`, validationError);
                    return {
                        content: [{
                            type: "text",
                            text: `Error validating collection creation: ${validationError.message || validationError}`
                        }],
                        isError: true
                    };
                }
            }

            // Load project-specific ignore patterns without polluting global context
            const projectPatterns = await this.loadProjectIgnorePatterns(absolutePath);
            
            // Add custom ignore patterns if provided
            // Add custom extensions if provided
            if (customFileExtensions.length > 0) {
                console.log(`[CUSTOM-EXTENSIONS] Adding ${customFileExtensions.length} custom extensions: ${customFileExtensions.join(', ')}`);
                this.codeContext.addCustomExtensions(customFileExtensions);
            }

            // Add custom ignore patterns if provided (before loading file-based patterns)
            if (customIgnorePatterns.length > 0) {
                console.log(`[IGNORE-PATTERNS] Adding ${customIgnorePatterns.length} custom ignore patterns: ${customIgnorePatterns.join(', ')}`);
                projectPatterns.push(...customIgnorePatterns);
            }

            // Create indexing task
            const taskId = this.generateTaskId(absolutePath);
            
            // Get effective ignore patterns (default + project-specific + custom)
            const allIgnorePatterns = [...DEFAULT_IGNORE_PATTERNS, ...projectPatterns];
            
            const task: IndexingTask = {
                taskId,
                path: absolutePath,
                status: 'queued',
                progress: 0,
                startTime: Date.now(),
                forceReindex,
                splitter: splitterType as 'ast' | 'langchain',
                ignorePatterns: allIgnorePatterns, // Include all patterns for display
                phase: 'Initializing',
                totalFiles: 0,
                processedFiles: 0,
                processedFileIndex: 0,
                totalChunks: 0,
                processedFilePaths: [] // Initialize empty array for file tracking
            };
            
            // Add task to management system
            this.snapshotManager.addTask(task);
            this.indexingTasks.set(taskId, task); // CRITICAL: Also add to memory map for API compatibility
            this.pathToTaskIdMap.set(absolutePath, taskId);
            
            // CRITICAL: Create indexed project record for tracking
            const collectionName = this.generateCollectionName(absolutePath);
            const projectRecord = {
                path: absolutePath,
                collectionName: collectionName,
                totalFiles: 0,
                totalChunks: 0,
                splitter: splitterType as 'ast' | 'langchain',
                indexedAt: new Date().toISOString(),
                status: 'indexing' as const
            };
            this.snapshotManager.addIndexedProject(projectRecord);
            
            // Add to indexing list and save snapshot immediately
            this.snapshotManager.addIndexingCodebase(absolutePath);
            this.snapshotManager.saveCodebaseSnapshot();

            // Track the codebase path for syncing
            trackCodebasePath(absolutePath);

            // Start background indexing - now safe to proceed
            this.startBackgroundIndexing(absolutePath, forceReindex, splitterType, taskId);

            const pathInfo = codebasePath !== absolutePath
                ? `\nNote: Input path '${codebasePath}' was resolved to absolute path '${absolutePath}'`
                : '';

            const extensionInfo = customFileExtensions.length > 0
                ? `\nUsing ${customFileExtensions.length} custom extensions: ${customFileExtensions.join(', ')}`
                : '';

            const ignoreInfo = customIgnorePatterns.length > 0
                ? `\nUsing ${customIgnorePatterns.length} custom ignore patterns: ${customIgnorePatterns.join(', ')}`
                : '';

            return {
                content: [{
                    type: "text",
                    text: `Started background indexing for codebase '${absolutePath}' using ${splitterType.toUpperCase()} splitter.${pathInfo}${extensionInfo}${ignoreInfo}\n\nIndexing is running in the background. You can search the codebase while indexing is in progress, but results may be incomplete until indexing completes.`
                }]
            };

        } catch (error: any) {
            // Enhanced error handling to prevent MCP service crash
            console.error('Error in handleIndexCodebase:', error);

            // Ensure we always return a proper MCP response, never throw
            return {
                content: [{
                    type: "text",
                    text: `Error starting indexing: ${error.message || error}`
                }],
                isError: true
            };
        }
    }

    private async startBackgroundIndexing(codebasePath: string, forceReindex: boolean, splitterType: string, taskId: string) {
        const absolutePath = codebasePath;

        // CRITICAL: Acquire indexing lock to prevent conflicts with other processes
        if (!this.syncManager.acquireIndexingLock()) {
            console.warn(`[BACKGROUND-INDEX] Another process is performing indexing/sync operations. Cannot start indexing ${absolutePath}`);
            // Update task status back to error since we couldn't acquire lock
            this.snapshotManager.updateTask(taskId, {
                status: 'error',
                lastError: 'Another MCP instance is performing indexing operations'
            });
            const memoryTask = this.indexingTasks.get(taskId);
            if (memoryTask) {
                memoryTask.status = 'error';
                memoryTask.lastError = 'Another MCP instance is performing indexing operations';
            }
            this.snapshotManager.saveCodebaseSnapshot();
            return;
        }

        console.log(`[BACKGROUND-INDEX] Starting background indexing for: ${absolutePath}`);
        
        // Update task status
        this.snapshotManager.updateTask(taskId, {
            status: 'indexing',
            phase: 'Starting indexing process'
        });
        this.snapshotManager.saveCodebaseSnapshot();

        // Note: If force reindex, collection was already cleared during validation phase
        if (forceReindex) {
            console.log(`[BACKGROUND-INDEX] ℹ️  Force reindex mode - collection was already cleared during validation`);
        }

        // Use the existing CodeContext instance for indexing.
        let contextForThisTask = this.codeContext;
        if (splitterType !== 'ast') {
            console.warn(`[BACKGROUND-INDEX] Non-AST splitter '${splitterType}' requested; falling back to AST splitter`);
        }

            // Generate collection name
            const normalizedPath = path.resolve(absolutePath);
            const hash = crypto.createHash('md5').update(normalizedPath).digest('hex');
            const collectionName = `code_chunks_${hash.substring(0, 8)}`;

            // Initialize file synchronizer with proper ignore patterns  
            const { FileSynchronizer } = await import("@zilliz/code-context-core");
            
            // Load project-specific ignore patterns for this indexing task
            const projectPatterns = await this.loadProjectIgnorePatterns(absolutePath);
            const allIgnorePatterns = [...DEFAULT_IGNORE_PATTERNS, ...projectPatterns];
            
            console.log(`[BACKGROUND-INDEX] Using ${allIgnorePatterns.length} ignore patterns (${DEFAULT_IGNORE_PATTERNS.length} default + ${projectPatterns.length} project-specific)`);
            const synchronizer = new FileSynchronizer(absolutePath, allIgnorePatterns);
            await synchronizer.initialize();

            // Store synchronizer in the context's internal map
            this.codeContext['synchronizers'].set(collectionName, synchronizer);
            if (contextForThisTask !== this.codeContext) {
                contextForThisTask['synchronizers'].set(collectionName, synchronizer);
            }

            console.log(`[BACKGROUND-INDEX] Starting indexing with ${splitterType} splitter for: ${absolutePath}`);

            // Log embedding provider information before indexing
            const embeddingProvider = this.codeContext['embedding'];
            console.log(`[BACKGROUND-INDEX] 🧠 Using embedding provider: ${embeddingProvider.getProvider()} with dimension: ${embeddingProvider.getDimension()}`);

            // Update task phase
            this.snapshotManager.updateTask(taskId, {
                phase: 'Indexing codebase'
            });
            
            // Start indexing with the appropriate context and progress callback
            console.log(`[BACKGROUND-INDEX] 🚀 Beginning codebase indexing process...`);
            
            // Create onFileProcessed callback for file-level tracking
            const onFileProcessed = (filePath: string): void => {
                const currentTask = this.snapshotManager.getTask(taskId);
                if (currentTask) {
                    // Initialize processedFilePaths if not exists
                    if (!currentTask.processedFilePaths) {
                        currentTask.processedFilePaths = [];
                    }
                    // Add processed file to the list
                    if (!currentTask.processedFilePaths.includes(filePath)) {
                        currentTask.processedFilePaths.push(filePath);
                        
                        // CRITICAL: Update the task in SnapshotManager properly
                        this.snapshotManager.updateTask(taskId, {
                            processedFilePaths: [...currentTask.processedFilePaths], // Create new array
                            processedFileIndex: currentTask.processedFilePaths.length,
                            // Provide rough estimate of chunks based on processed files
                            totalChunks: Math.floor(currentTask.processedFilePaths.length * 7) // ~7 chunks per file estimate
                        });
                        
                        // Also update memory task for API compatibility
                        const memoryTask = this.indexingTasks.get(taskId);
                        if (memoryTask) {
                            if (!memoryTask.processedFilePaths) {
                                memoryTask.processedFilePaths = [];
                            }
                            if (!memoryTask.processedFilePaths.includes(filePath)) {
                                memoryTask.processedFilePaths.push(filePath);
                                memoryTask.processedFileIndex = memoryTask.processedFilePaths.length;
                                memoryTask.totalChunks = Math.floor(memoryTask.processedFilePaths.length * 7);
                            }
                        }
                        
                        // Save snapshot with longer delay to avoid too frequent writes
                        this.snapshotManager.saveCodebaseSnapshot();
                    }
                }
            };
            
            // CRITICAL: Temporarily set project-specific ignore patterns before indexing
            const originalIgnorePatterns = this.codeContext['ignorePatterns'];
            this.codeContext['ignorePatterns'] = allIgnorePatterns;
            
            // Create pause/cancel check callback that will be called at key points during indexing
            const pauseCheck = async (): Promise<void> => {
                const currentTask = this.indexingTasks.get(taskId);
                if (currentTask) {
                    if (currentTask.status === 'paused') {
                        console.log(`[BACKGROUND-INDEX] ⏸️  Task ${taskId} is paused, stopping indexing process...`);
                        throw new Error('TASK_PAUSED');
                    }
                    if (currentTask.status === 'cancelling') {
                        console.log(`[BACKGROUND-INDEX] 🗑️ Task ${taskId} is cancelling, stopping indexing process...`);
                        throw new Error('TASK_CANCELLED');
                    }
                }
            };

            try {
                const stats = await contextForThisTask.indexCodebase(absolutePath, {
                progressCallback: (progressInfo) => {
                    // Update task progress in SnapshotManager
                    this.snapshotManager.updateTask(taskId, {
                        totalFiles: progressInfo.total,
                        processedFiles: progressInfo.current,
                        processedFileIndex: progressInfo.current,
                        phase: progressInfo.phase || 'Processing files',
                        progress: progressInfo.total > 0 ? 
                            (progressInfo.current / progressInfo.total) * 100 : 0
                    });
                    
                    // CRITICAL: Also update indexedProjects record with current progress
                    this.snapshotManager.updateIndexedProject(absolutePath, {
                        totalFiles: progressInfo.total,
                        // Provide rough estimate of chunks during indexing
                        totalChunks: Math.floor(progressInfo.current * 7) // ~7 chunks per processed file
                    });
                    
                    // CRITICAL: Also update indexingTasks for API compatibility
                    const memoryTask = this.indexingTasks.get(taskId);
                    if (memoryTask) {
                        memoryTask.totalFiles = progressInfo.total;
                        memoryTask.processedFiles = progressInfo.current;
                        memoryTask.processedFileIndex = progressInfo.current;
                        memoryTask.phase = progressInfo.phase || 'Processing files';
                        memoryTask.progress = progressInfo.total > 0 ? 
                            (progressInfo.current / progressInfo.total) * 100 : 0;
                    }
                    
                    // Save snapshot periodically (every 10 files)
                    if (progressInfo.current % 10 === 0) {
                        this.snapshotManager.saveCodebaseSnapshot();
                    }
                },
                pauseCheck,
                onFileProcessed
            });
            console.log(`[BACKGROUND-INDEX] ✅ Indexing completed successfully! Files: ${stats.indexedFiles}, Chunks: ${stats.totalChunks}`);

            // Move from indexing to indexed list - update task status with final stats
            const completedTask = this.snapshotManager.getTask(taskId);
            if (completedTask) {
                this.snapshotManager.updateTask(taskId, {
                    status: 'completed',
                    endTime: Date.now(),
                    totalChunks: stats.totalChunks,
                    processedFiles: stats.indexedFiles,
                    totalFiles: stats.indexedFiles,  // CRITICAL: 也要更新totalFiles！
                    progress: 100,
                    phase: 'Completed'
                });
                
                // CRITICAL: Also update indexedProjects record with final stats
                this.snapshotManager.updateIndexedProject(absolutePath, {
                    totalFiles: stats.indexedFiles,
                    totalChunks: stats.totalChunks,
                    status: 'completed',
                    indexedAt: new Date().toISOString()
                });
                
                // Also update memory task
                const memoryTask = this.indexingTasks.get(taskId);
                if (memoryTask) {
                    memoryTask.status = 'completed';
                    memoryTask.endTime = Date.now();
                    memoryTask.totalChunks = stats.totalChunks;
                    memoryTask.processedFiles = stats.indexedFiles;
                    memoryTask.totalFiles = stats.indexedFiles;  // CRITICAL: 也要更新totalFiles！
                    memoryTask.progress = 100;
                    memoryTask.phase = 'Completed';
                }
            }

            // Move from indexing to indexed list
            this.snapshotManager.moveFromIndexingToIndexed(absolutePath);
            this.indexingStats = { indexedFiles: stats.indexedFiles, totalChunks: stats.totalChunks };

            // Save snapshot after updating codebase lists
            this.snapshotManager.saveCodebaseSnapshot();

            let message = `Background indexing completed for '${absolutePath}' using ${splitterType.toUpperCase()} splitter.\nIndexed ${stats.indexedFiles} files, ${stats.totalChunks} chunks.`;
            if (stats.status === 'limit_reached') {
                message += `\n⚠️  Warning: Indexing stopped because the chunk limit (450,000) was reached. The index may be incomplete.`;
            }

            console.log(`[BACKGROUND-INDEX] ${message}`);
            
            // CRITICAL: Release indexing lock after successful completion
            this.syncManager.releaseIndexingLock();

            } catch (error: any) {
                // Handle task cancellation
                if (error.message === 'TASK_CANCELLED') {
                    console.log(`[BACKGROUND-INDEX] 🗑️ Indexing cancelled for ${absolutePath}. Cleaning up...`);
                    try {
                        // CRITICAL: Clear any partially created index to prevent orphans
                        await this.codeContext.clearIndex(absolutePath);
                        console.log(`[BACKGROUND-INDEX] ✅ Partially created index for ${absolutePath} cleared.`);
                    } catch (cleanupError: any) {
                        console.error(`[BACKGROUND-INDEX] ❌ Failed to clean up index for cancelled task ${absolutePath}:`, cleanupError);
                    } finally {
                        // CRITICAL: Release indexing lock when task is cancelled during indexing
                        this.syncManager.releaseIndexingLock();
                        
                        // Remove the task completely
                        this.indexingTasks.delete(taskId);
                        
                        // Also remove from the snapshot manager
                        this.snapshotManager.removeIndexingCodebase(absolutePath);
                        this.snapshotManager.saveCodebaseSnapshot();
                    }
                    return;
                }
                
                // Handle task pause differently from actual errors
                if (error.message === 'TASK_PAUSED') {
                    console.log(`[BACKGROUND-INDEX] ⏸️  Indexing paused for ${absolutePath}`);
                    // Task is already marked as paused by the web API
                    // No need to change status or save snapshot
                    
                    // CRITICAL: Release indexing lock when task is paused
                    this.syncManager.releaseIndexingLock();
                    return;
                }
                
                console.error(`[BACKGROUND-INDEX] Error during indexing for ${absolutePath}:`, error);
                
                // Update task with error status
                this.snapshotManager.updateTask(taskId, {
                    status: 'error',
                    endTime: Date.now(),
                    lastError: error.message || String(error),
                    phase: 'Failed'
                });
                
                // CRITICAL: Update failed task in memory maps but keep it
                const memoryTask = this.indexingTasks.get(taskId);
                if (memoryTask) {
                    memoryTask.status = 'error';
                    memoryTask.endTime = Date.now();
                    memoryTask.lastError = error.message || String(error);
                    memoryTask.phase = 'Failed';
                }
                
                // Remove from indexing list on error
                this.snapshotManager.removeIndexingCodebase(absolutePath);
                this.snapshotManager.saveCodebaseSnapshot();

                // Log error but don't crash MCP service - indexing errors are handled gracefully
                console.error(`[BACKGROUND-INDEX] Indexing failed for ${absolutePath}: ${error.message || error}`);
                
                // CRITICAL: Release indexing lock after error handling
                this.syncManager.releaseIndexingLock();
            } finally {
                // CRITICAL: Always restore original ignore patterns to prevent pollution
                this.codeContext['ignorePatterns'] = originalIgnorePatterns;
            }
    }

    public async handleSearchCode(args: any) {
        const { path: codebasePath, query, limit = 10 } = args;
        const resultLimit = limit || 10;

        try {
            // CRITICAL: Don't sync from cloud during search - it can disrupt ongoing indexing
            // Cloud sync should only happen during index operations, not search operations
            
            // CRITICAL: Refresh snapshot data to get latest indexing status
            this.snapshotManager.loadCodebaseSnapshot();
            
            // Force absolute path resolution - warn if relative path provided
            const absolutePath = ensureAbsolutePath(codebasePath);

            // Validate path exists
            if (!fs.existsSync(absolutePath)) {
                return {
                    content: [{
                        type: "text",
                        text: `Error: Path '${absolutePath}' does not exist. Original input: '${codebasePath}'`
                    }],
                    isError: true
                };
            }

            // Check if it's a directory
            const stat = fs.statSync(absolutePath);
            if (!stat.isDirectory()) {
                return {
                    content: [{
                        type: "text",
                        text: `Error: Path '${absolutePath}' is not a directory`
                    }],
                    isError: true
                };
            }

            trackCodebasePath(absolutePath);

            // Check if this codebase is indexed or being indexed
            const isIndexed = this.snapshotManager.getIndexedCodebases().includes(absolutePath);
            const isIndexing = this.snapshotManager.getIndexingCodebases().includes(absolutePath);

            if (!isIndexed && !isIndexing) {
                return {
                    content: [{
                        type: "text",
                        text: `Error: Codebase '${absolutePath}' is not indexed. Please index it first using the index_codebase tool.`
                    }],
                    isError: true
                };
            }

            // Show indexing status if codebase is being indexed
            let indexingStatusMessage = '';
            if (isIndexing) {
                indexingStatusMessage = `\n⚠️  **Indexing in Progress**: This codebase is currently being indexed in the background. Search results may be incomplete until indexing completes.`;
            }

            console.log(`[SEARCH] Searching in codebase: ${absolutePath}`);
            console.log(`[SEARCH] Query: "${query}"`);
            console.log(`[SEARCH] Indexing status: ${isIndexing ? 'In Progress' : 'Completed'}`);

            // Log embedding provider information before search
            const embeddingProvider = this.codeContext['embedding'];
            console.log(`[SEARCH] 🧠 Using embedding provider: ${embeddingProvider.getProvider()} for semantic search`);
            console.log(`[SEARCH] 🔍 Generating embeddings for query using ${embeddingProvider.getProvider()}...`);

            // Search in the specified codebase
            const searchResults = await this.codeContext.semanticSearch(
                absolutePath,
                query,
                Math.min(resultLimit, 50),
                0.3
            );

            console.log(`[SEARCH] ✅ Search completed! Found ${searchResults.length} results using ${embeddingProvider.getProvider()} embeddings`);

            if (searchResults.length === 0) {
                let noResultsMessage = `No results found for query: "${query}" in codebase '${absolutePath}'`;
                if (isIndexing) {
                    noResultsMessage += `\n\nNote: This codebase is still being indexed. Try searching again after indexing completes, or the query may not match any indexed content.`;
                }
                return {
                    content: [{
                        type: "text",
                        text: noResultsMessage
                    }]
                };
            }

            // Format results
            const formattedResults = searchResults.map((result: any, index: number) => {
                const location = `${result.relativePath}:${result.startLine}-${result.endLine}`;
                const context = truncateContent(result.content, 5000);
                const codebaseInfo = path.basename(absolutePath);

                return `${index + 1}. Code snippet (${result.language}) [${codebaseInfo}]\n` +
                    `   Location: ${location}\n` +
                    `   Score: ${result.score.toFixed(3)}\n` +
                    `   Context: \n\`\`\`${result.language}\n${context}\n\`\`\`\n`;
            }).join('\n');

            let resultMessage = `Found ${searchResults.length} results for query: "${query}" in codebase '${absolutePath}'${indexingStatusMessage}\n\n${formattedResults}`;

            if (isIndexing) {
                resultMessage += `\n\n💡 **Tip**: This codebase is still being indexed. More results may become available as indexing progresses.`;
            }

            return {
                content: [{
                    type: "text",
                    text: resultMessage
                }]
            };
        } catch (error) {
            // Check if this is the collection limit error
            // Handle both direct string throws and Error objects containing the message
            const errorMessage = typeof error === 'string' ? error : (error instanceof Error ? error.message : String(error));

            if (errorMessage === COLLECTION_LIMIT_MESSAGE || errorMessage.includes(COLLECTION_LIMIT_MESSAGE)) {
                // Return the collection limit message as a successful response
                // This ensures LLM treats it as final answer, not as retryable error
                return {
                    content: [{
                        type: "text",
                        text: COLLECTION_LIMIT_MESSAGE
                    }]
                };
            }

            return {
                content: [{
                    type: "text",
                    text: `Error searching code: ${errorMessage} Please check if the codebase has been indexed first.`
                }],
                isError: true
            };
        }
    }

    public async handleClearIndex(args: any) {
        const { path: codebasePath } = args;

        if (this.snapshotManager.getIndexedCodebases().length === 0 && this.snapshotManager.getIndexingCodebases().length === 0) {
            return {
                content: [{
                    type: "text",
                    text: "No codebases are currently indexed or being indexed."
                }]
            };
        }

        try {
            // Force absolute path resolution - warn if relative path provided
            const absolutePath = ensureAbsolutePath(codebasePath);

            // Validate path exists
            if (!fs.existsSync(absolutePath)) {
                return {
                    content: [{
                        type: "text",
                        text: `Error: Path '${absolutePath}' does not exist. Original input: '${codebasePath}'`
                    }],
                    isError: true
                };
            }

            // Check if it's a directory
            const stat = fs.statSync(absolutePath);
            if (!stat.isDirectory()) {
                return {
                    content: [{
                        type: "text",
                        text: `Error: Path '${absolutePath}' is not a directory`
                    }],
                    isError: true
                };
            }

            // Check if this codebase is indexed or being indexed
            const isIndexed = this.snapshotManager.getIndexedCodebases().includes(absolutePath);
            const isIndexing = this.snapshotManager.getIndexingCodebases().includes(absolutePath);

            if (!isIndexed && !isIndexing) {
                return {
                    content: [{
                        type: "text",
                        text: `Error: Codebase '${absolutePath}' is not indexed or being indexed.`
                    }],
                    isError: true
                };
            }

            console.log(`[CLEAR] Clearing codebase: ${absolutePath}`);

            try {
                await this.codeContext.clearIndex(absolutePath);
                console.log(`[CLEAR] Successfully cleared index for: ${absolutePath}`);
            } catch (error: any) {
                const errorMsg = `Failed to clear ${absolutePath}: ${error.message}`;
                console.error(`[CLEAR] ${errorMsg}`);
                return {
                    content: [{
                        type: "text",
                        text: errorMsg
                    }],
                    isError: true
                };
            }

            // Remove the cleared codebase from both lists
            this.snapshotManager.removeIndexedCodebase(absolutePath);
            this.snapshotManager.removeIndexingCodebase(absolutePath);
            this.snapshotManager.removeProject(absolutePath);
            
            // Remove any associated tasks
            const taskId = this.pathToTaskIdMap.get(absolutePath);
            if (taskId) {
                this.snapshotManager.removeTask(taskId);
                this.indexingTasks.delete(taskId); // CRITICAL: Also remove from memory map
                this.pathToTaskIdMap.delete(absolutePath);
            }

            // Reset indexing stats if this was the active codebase
            this.indexingStats = null;

            // Save snapshot after clearing index
            this.snapshotManager.saveCodebaseSnapshot();

            let resultText = `Successfully cleared codebase '${absolutePath}'`;

            const remainingIndexed = this.snapshotManager.getIndexedCodebases().length;
            const remainingIndexing = this.snapshotManager.getIndexingCodebases().length;

            if (remainingIndexed > 0 || remainingIndexing > 0) {
                resultText += `\n${remainingIndexed} other indexed codebase(s) and ${remainingIndexing} indexing codebase(s) remain`;
            }

            return {
                content: [{
                    type: "text",
                    text: resultText
                }]
            };
        } catch (error) {
            // Check if this is the collection limit error
            // Handle both direct string throws and Error objects containing the message
            const errorMessage = typeof error === 'string' ? error : (error instanceof Error ? error.message : String(error));

            if (errorMessage === COLLECTION_LIMIT_MESSAGE || errorMessage.includes(COLLECTION_LIMIT_MESSAGE)) {
                // Return the collection limit message as a successful response
                // This ensures LLM treats it as final answer, not as retryable error
                return {
                    content: [{
                        type: "text",
                        text: COLLECTION_LIMIT_MESSAGE
                    }]
                };
            }

            return {
                content: [{
                    type: "text",
                    text: `Error clearing index: ${errorMessage}`
                }],
                isError: true
            };
        }
    }

    /**
     * Load project-specific ignore patterns without polluting global context
     * @param codebasePath Path to the codebase
     * @returns Array of project-specific ignore patterns
     */
    private async loadProjectIgnorePatterns(codebasePath: string): Promise<string[]> {
        const projectPatterns: string[] = [];
        
        try {
            console.log(`[IGNORE-PATTERNS] Loading ignore patterns from codebase files...`);
            
            // 1. Load .gitignore
            const gitignorePath = path.join(codebasePath, '.gitignore');
            const gitignorePatterns = await this.loadIgnoreFile(gitignorePath, '.gitignore');
            projectPatterns.push(...gitignorePatterns);

            // 2. Load all .xxxignore files in codebase directory
            const ignoreFiles = await this.findIgnoreFiles(codebasePath);
            for (const ignoreFile of ignoreFiles) {
                const patterns = await this.loadIgnoreFile(ignoreFile, path.basename(ignoreFile));
                projectPatterns.push(...patterns);
            }

            // 3. Load global ~/.codecontext/.codecontextignore
            const globalIgnorePatterns = await this.loadGlobalIgnoreFile();
            projectPatterns.push(...globalIgnorePatterns);

            if (projectPatterns.length > 0) {
                console.log(`🚫 Loaded total ${projectPatterns.length} ignore patterns from all ignore files`);
            } else {
                console.log('📄 No ignore files found');
            }
            
            return projectPatterns;
        } catch (error) {
            console.warn(`⚠️ Failed to load ignore patterns: ${error}`);
            return [];
        }
    }

    /**
     * Find all .xxxignore files in the codebase directory (excluding .gitignore as it's handled separately)
     */
    private async findIgnoreFiles(codebasePath: string): Promise<string[]> {
        try {
            const entries = await fs.promises.readdir(codebasePath, { withFileTypes: true });
            const ignoreFiles: string[] = [];

            for (const entry of entries) {
                if (entry.isFile() &&
                    entry.name.startsWith('.') &&
                    entry.name.endsWith('ignore') &&
                    entry.name !== '.gitignore') {
                    ignoreFiles.push(path.join(codebasePath, entry.name));
                }
            }

            if (ignoreFiles.length > 0) {
                console.log(`📄 Found additional ignore files: ${ignoreFiles.map(f => path.basename(f)).join(', ')}`);
            }

            return ignoreFiles;
        } catch (error) {
            console.warn(`⚠️ Failed to scan for ignore files: ${error}`);
            return [];
        }
    }

    /**
     * Load global ignore file from ~/.codecontext/.codecontextignore
     */
    private async loadGlobalIgnoreFile(): Promise<string[]> {
        try {
            const homeDir = require('os').homedir();
            const globalIgnorePath = path.join(homeDir, '.codecontext', '.codecontextignore');
            return await this.loadIgnoreFile(globalIgnorePath, 'global .codecontextignore');
        } catch (error) {
            return [];
        }
    }

    /**
     * Load ignore patterns from a specific ignore file
     */
    private async loadIgnoreFile(filePath: string, fileName: string): Promise<string[]> {
        try {
            await fs.promises.access(filePath);
            console.log(`📄 Found ${fileName} file at: ${filePath}`);

            const content = await fs.promises.readFile(filePath, 'utf-8');
            const patterns = content
                .split('\n')
                .map(line => line.trim())
                .filter(line => line && !line.startsWith('#'));

            if (patterns.length > 0) {
                console.log(`🚫 Loaded ${patterns.length} ignore patterns from ${fileName}`);
                return patterns;
            } else {
                console.log(`📄 ${fileName} file found but no valid patterns detected`);
                return [];
            }
        } catch (error) {
            if (fileName === '.gitignore' || fileName.includes('global')) {
                console.log(`📄 No ${fileName} file found`);
            }
            return [];
        }
    }

    /**
     * Generate collection name from codebase path
     */
    private generateCollectionName(codebasePath: string): string {
        const normalizedPath = path.resolve(codebasePath);
        const hash = crypto.createHash('md5').update(normalizedPath).digest('hex');
        return `code_chunks_${hash.substring(0, 8)}`;
    }
} 