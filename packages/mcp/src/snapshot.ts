import * as fs from "fs";
import * as path from "path";
import * as os from "os";
import { CodebaseSnapshot, IndexedProject, IndexingTask } from "./config.js";

export class SnapshotManager {
    private snapshotFilePath: string;
    private indexedCodebases: string[] = [];
    private indexingCodebases: string[] = [];
    private indexedProjects: IndexedProject[] = [];
    private activeTasks: Map<string, IndexingTask> = new Map();

    constructor() {
        // Initialize snapshot file path
        this.snapshotFilePath = path.join(os.homedir(), '.codecontext', 'mcp-codebase-snapshot.json');
    }

    public getIndexedCodebases(): string[] {
        return [...this.indexedCodebases];
    }

    public getIndexingCodebases(): string[] {
        return [...this.indexingCodebases];
    }

    public getIndexedProjects(): IndexedProject[] {
        return [...this.indexedProjects];
    }

    public getActiveTasks(): IndexingTask[] {
        return Array.from(this.activeTasks.values());
    }

    public getTask(taskId: string): IndexingTask | undefined {
        return this.activeTasks.get(taskId);
    }

    public addTask(task: IndexingTask): void {
        this.activeTasks.set(task.taskId, task);
    }

    public updateTask(taskId: string, updates: Partial<IndexingTask>): void {
        const task = this.activeTasks.get(taskId);
        if (task) {
            Object.assign(task, updates);
        }
    }

    public removeTask(taskId: string): void {
        this.activeTasks.delete(taskId);
    }

    public addProject(project: IndexedProject): void {
        const existingIndex = this.indexedProjects.findIndex(p => p.path === project.path);
        if (existingIndex >= 0) {
            this.indexedProjects[existingIndex] = project;
        } else {
            this.indexedProjects.push(project);
        }
    }

    public removeProject(path: string): void {
        this.indexedProjects = this.indexedProjects.filter(p => p.path !== path);
    }

    public getProject(path: string): IndexedProject | undefined {
        return this.indexedProjects.find(p => p.path === path);
    }

    public addIndexingCodebase(codebasePath: string): void {
        if (!this.indexingCodebases.includes(codebasePath)) {
            this.indexingCodebases.push(codebasePath);
        }
    }

    public removeIndexingCodebase(codebasePath: string): void {
        this.indexingCodebases = this.indexingCodebases.filter(path => path !== codebasePath);
    }

    public addIndexedCodebase(codebasePath: string): void {
        if (!this.indexedCodebases.includes(codebasePath)) {
            this.indexedCodebases.push(codebasePath);
        }
    }

    public removeIndexedCodebase(codebasePath: string): void {
        this.indexedCodebases = this.indexedCodebases.filter(path => path !== codebasePath);
    }

    public moveFromIndexingToIndexed(codebasePath: string): void {
        this.removeIndexingCodebase(codebasePath);
        this.addIndexedCodebase(codebasePath);
    }

    public loadCodebaseSnapshot(): void {
        console.log('[SNAPSHOT-DEBUG] Loading codebase snapshot from:', this.snapshotFilePath);

        try {
            if (!fs.existsSync(this.snapshotFilePath)) {
                console.log('[SNAPSHOT-DEBUG] Snapshot file does not exist. Starting with empty codebase list.');
                return;
            }

            const snapshotData = fs.readFileSync(this.snapshotFilePath, 'utf8');
            const snapshot: CodebaseSnapshot = JSON.parse(snapshotData);

            console.log('[SNAPSHOT-DEBUG] Loaded snapshot:', snapshot);

            // Validate that the codebases still exist
            const validCodebases: string[] = [];
            for (const codebasePath of snapshot.indexedCodebases) {
                if (fs.existsSync(codebasePath)) {
                    validCodebases.push(codebasePath);
                    console.log(`[SNAPSHOT-DEBUG] Validated codebase: ${codebasePath}`);
                } else {
                    console.warn(`[SNAPSHOT-DEBUG] Codebase no longer exists, removing: ${codebasePath}`);
                }
            }

            // Handle indexing codebases - check if they have active tasks
            const validIndexingCodebases: string[] = [];
            for (const codebasePath of snapshot.indexingCodebases || []) {
                if (fs.existsSync(codebasePath)) {
                    // Check if there's an active indexing task for this codebase
                    const hasActiveIndexingTask = Array.from(this.activeTasks.values())
                        .some(task => task.path === codebasePath && 
                              (task.status === 'indexing' || task.status === 'queued' || task.status === 'paused'));
                    
                    if (hasActiveIndexingTask) {
                        validIndexingCodebases.push(codebasePath);
                        console.log(`[SNAPSHOT-DEBUG] Found active indexing codebase: ${codebasePath}`);
                    } else {
                        console.warn(`[SNAPSHOT-DEBUG] Found indexing codebase without active task, treating as interrupted: ${codebasePath}`);
                    }
                } else {
                    console.warn(`[SNAPSHOT-DEBUG] Indexing codebase no longer exists: ${codebasePath}`);
                }
            }

            // Restore indexed projects
            this.indexedProjects = (snapshot.indexedProjects || []).filter(project => {
                if (fs.existsSync(project.path)) {
                    console.log(`[SNAPSHOT-DEBUG] Restored project: ${path.basename(project.path)} at ${project.path}`);
                    return true;
                } else {
                    console.warn(`[SNAPSHOT-DEBUG] Project path no longer exists, removing: ${project.path}`);
                    return false;
                }
            });

            // Restore active tasks (convert to Map)
            this.activeTasks.clear();
            for (const task of snapshot.activeTasks || []) {
                if (fs.existsSync(task.path)) {
                    // If server restarted while task was running, set to 'paused'
                    if (task.status === 'indexing') {
                        console.warn(`[SNAPSHOT-DEBUG] Found interrupted indexing task for ${task.path}. Setting status to 'paused'.`);
                        task.status = 'paused';
                    }
                    
                    // Skip cancelling tasks
                    if (task.status === 'cancelling') {
                        console.warn(`[SNAPSHOT-DEBUG] Found cancelling task for ${task.path}. Skipping task restoration.`);
                        continue;
                    }
                    
                    this.activeTasks.set(task.taskId, task);
                    console.log(`[SNAPSHOT-DEBUG] Restored task: ${task.taskId} for ${task.path}`);
                } else {
                    console.warn(`[SNAPSHOT-DEBUG] Task path no longer exists, removing task: ${task.path}`);
                }
            }

            // Restore state - preserve active indexing codebases
            this.indexedCodebases = validCodebases;
            this.indexingCodebases = validIndexingCodebases; // CRITICAL: Preserve active indexing tasks

            console.log(`[SNAPSHOT-DEBUG] Restored ${validCodebases.length} fully indexed codebases.`);
            console.log(`[SNAPSHOT-DEBUG] Restored ${validIndexingCodebases.length} active indexing codebases.`);
            console.log(`[SNAPSHOT-DEBUG] Restored ${this.indexedProjects.length} indexed projects.`);
            console.log(`[SNAPSHOT-DEBUG] Restored ${this.activeTasks.size} active/paused tasks.`);

            // Save updated snapshot if we removed any invalid paths
            if (validCodebases.length !== snapshot.indexedCodebases.length ||
                validIndexingCodebases.length !== (snapshot.indexingCodebases?.length || 0)) {
                this.saveCodebaseSnapshot();
            }

        } catch (error: any) {
            console.error('[SNAPSHOT-DEBUG] Error loading snapshot:', error);
            console.log('[SNAPSHOT-DEBUG] Starting with empty codebase list due to snapshot error.');
        }
    }

    public saveCodebaseSnapshot(): void {
        console.log('[SNAPSHOT-DEBUG] Saving codebase snapshot to:', this.snapshotFilePath);

        try {
            // Ensure directory exists
            const snapshotDir = path.dirname(this.snapshotFilePath);
            if (!fs.existsSync(snapshotDir)) {
                fs.mkdirSync(snapshotDir, { recursive: true });
                console.log('[SNAPSHOT-DEBUG] Created snapshot directory:', snapshotDir);
            }

            const snapshot: CodebaseSnapshot = {
                indexedCodebases: this.indexedCodebases,
                indexingCodebases: this.indexingCodebases,
                indexedProjects: this.indexedProjects,
                activeTasks: Array.from(this.activeTasks.values()),
                lastUpdated: new Date().toISOString()
            };

            fs.writeFileSync(this.snapshotFilePath, JSON.stringify(snapshot, null, 2));
            console.log('[SNAPSHOT-DEBUG] Snapshot saved successfully. Indexed codebases:', this.indexedCodebases.length, 'Indexing codebases:', this.indexingCodebases.length, 'Projects:', this.indexedProjects.length, 'Tasks:', this.activeTasks.size);

        } catch (error: any) {
            console.error('[SNAPSHOT-DEBUG] Error saving snapshot:', error);
        }
    }

    /**
     * Add indexed project record
     */
    public addIndexedProject(project: IndexedProject): void {
        // Check if project already exists, if so update it
        const existingIndex = this.indexedProjects.findIndex(p => p.path === project.path);
        if (existingIndex >= 0) {
            this.indexedProjects[existingIndex] = project;
            console.log(`[SNAPSHOT-DEBUG] Updated indexed project: ${project.path}`);
        } else {
            this.indexedProjects.push(project);
            console.log(`[SNAPSHOT-DEBUG] Added indexed project: ${project.path}`);
        }
    }

    /**
     * Update indexed project record
     */
    public updateIndexedProject(path: string, updates: Partial<IndexedProject>): void {
        const projectIndex = this.indexedProjects.findIndex(p => p.path === path);
        if (projectIndex >= 0) {
            this.indexedProjects[projectIndex] = {
                ...this.indexedProjects[projectIndex],
                ...updates
            };
            console.log(`[SNAPSHOT-DEBUG] Updated indexed project ${path}:`, updates);
        } else {
            console.warn(`[SNAPSHOT-DEBUG] Project not found for update: ${path}`);
        }
    }

    /**
     * Fix data consistency issues in completed tasks
     * Sets processedFiles = totalFiles for completed tasks where processedFiles is 0
     */
    public fixCompletedTasksData(): void {
        let fixedCount = 0;
        for (const task of this.activeTasks.values()) {
            if (task.status === 'completed' && task.processedFiles === 0 && task.totalFiles > 0) {
                console.log(`[SNAPSHOT-DEBUG] Fixing data inconsistency for task ${task.taskId}: setting processedFiles from 0 to ${task.totalFiles}`);
                task.processedFiles = task.totalFiles;
                fixedCount++;
            }
        }
        
        if (fixedCount > 0) {
            console.log(`[SNAPSHOT-DEBUG] Fixed ${fixedCount} tasks with data inconsistency`);
            this.saveCodebaseSnapshot();
        }
    }
} 