{"name": "@zilliz/code-context-mcp", "version": "0.0.11", "description": "Model Context Protocol integration for Code Context", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": "dist/index.js", "scripts": {"build": "pnpm clean && tsc --build --force", "dev": "tsx --watch src/index.ts", "clean": "rm -rf dist", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "typecheck": "tsc --noEmit", "start": "tsx src/index.ts", "start:with-env": "OPENAI_API_KEY=${OPENAI_API_KEY:your-api-key-here} MILVUS_ADDRESS=${MILVUS_ADDRESS:localhost:19530} tsx src/index.ts", "prepublishOnly": "pnpm build"}, "dependencies": {"@zilliz/code-context-core": "workspace:*", "@modelcontextprotocol/sdk": "^1.12.1", "cors": "^2.8.5", "express": "^4.18.2", "p-limit": "^6.2.0", "zod": "^3.25.55"}, "devDependencies": {"@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/node": "^20.0.0", "tsx": "^4.19.4", "typescript": "^5.0.0"}, "files": ["dist", "README.md"], "repository": {"type": "git", "url": "https://github.com/zilliztech/code-context.git", "directory": "packages/mcp"}, "license": "MIT", "publishConfig": {"access": "public"}}