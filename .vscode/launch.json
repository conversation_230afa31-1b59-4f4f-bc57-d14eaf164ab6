// A launch configuration that compiles the extension and then opens it inside a new window
// Use IntelliSense to learn about possible attributes.
// Hover to view descriptions of existing attributes.
// For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
{
	"version": "0.2.0",
	"configurations": [
		{
			"name": "Run Extension",
			"type": "extensionHost",
			"request": "launch",
			"args": [
				"--extensionDevelopmentPath=${workspaceFolder}/packages/vscode-extension"
			],
			"outFiles": [
				"${workspaceFolder}/packages/vscode-extension/dist/**/*.js"
			],
			"preLaunchTask": "npm: webpack:dev - packages/vscode-extension"
		},
		{
			"name": "Extension Tests",
			"type": "extensionHost",
			"request": "launch",
			"args": [
				"--extensionDevelopmentPath=${workspaceFolder}/packages/vscode-extension",
				"--extensionTestsPath=${workspaceFolder}/packages/vscode-extension/dist/test/suite/index"
			],
			"outFiles": [
				"${workspaceFolder}/packages/vscode-extension/dist/test/**/*.js"
			],
			"preLaunchTask": "npm: webpack:dev - packages/vscode-extension"
		}
	]
}